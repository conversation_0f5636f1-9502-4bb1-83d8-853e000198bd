<template>
	<view class="wrap">
	  <!-- <view class="tipinfo">以下信息仅供参考，请勿转载用于其它商业用途!</view>
	  <view class="warnTitle">如若没有显示的信息请打开网址查看</view> -->
	  <view class="list">
		<u-row customStyle="margin-bottom: 10px;">
		  <u-col span="12">
			<span class="left-title">项目名称：</span>
		  </u-col>
		</u-row>
		<u-row customStyle="margin-bottom: 20px;">
		  <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
			<span @click="copyText(result.name)">{{ result.name }}</span>
		  </u-col>
		</u-row>
		<u-row customStyle="margin-bottom: 10px;">
		  <u-col span="12">
			<span class="left-title">项目编号：</span>
		  </u-col>
		</u-row>
		<u-row customStyle="margin-bottom: 20px;">
		  <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
			<span @click="copyText(result.code)">{{ result.code }}</span>
		  </u-col>
		</u-row>
		<u-row customStyle="margin-bottom: 10px;">
		  <u-col span="12">
			<span class="left-title">地区：</span>
		  </u-col>
		</u-row>
		<u-row customStyle="margin-bottom: 20px;">
		  <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
			<span @click="copyText(result.region)">{{ result.region }}</span>
		  </u-col>
		</u-row>
		<u-row customStyle="margin-bottom: 10px;">
		  <u-col span="12">
			<span class="left-title">联系人名称：</span>
		  </u-col>
		</u-row>
		<u-row customStyle="margin-bottom: 20px;">
		  <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
			<span @click="copyText(result.bidPopName)">{{ result.bidPopName }}</span>
		  </u-col>
		</u-row>
		<u-row customStyle="margin-bottom: 10px;">
		  <u-col span="12">
			<span class="left-title">项目金额：</span>
		  </u-col>
		</u-row>
		<u-row customStyle="margin-bottom: 20px;">
		  <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
			<span>{{ result.sum ? result.sum + '元' : '' }}</span>
		  </u-col>
		</u-row>
		<u-row customStyle="margin-bottom: 10px;">
		  <u-col span="12">
			<span class="left-title">招标单位：</span>
		  </u-col>
		</u-row>
		<u-row customStyle="margin-bottom: 20px;">
		  <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
			<span @click="copyText(result.bidPop)">{{ result.bidPop }}</span>
		  </u-col>
		</u-row>
		<u-row customStyle="margin-bottom: 10px;">
		  <u-col span="12">
			<span class="left-title">中标单位：</span>
		  </u-col>
		</u-row>
		<u-row customStyle="margin-bottom: 20px;">
		  <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
			<span @click="copyText(result.winPop)">{{ result.winPop }}</span>
		  </u-col>
		</u-row>
		<!-- <u-row>
		  <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
			<u-button text="复制链接" @click="copyUrl"></u-button>
		  </u-col>
		</u-row> -->
	  </view>
	</view>
  </template>
  
  <script>
  import {download} from "@/utils/download";
  
  export default {
	data() {
	  return {
		id: '',
		name: '',
		result: '',
		content: '',
		fileResult: [],
	  }
	},
	onLoad(options) {
	  if (!uni.getStorageSync('accessToken')) {
		uni.navigateTo({
		  url: '/pages/login/login-account'
		})
	  } else {
		this.id = options.id
		this.getDetail()
	  }
	},
	methods: {
	  download(url, type) {
		download(url, type)
	  },
	  copyUrl() {
		uni.setClipboardData({
		  data: this.result.url,
		  success: function () {
			uni.showToast({
			  title: '复制成功',
			  icon: 'none'
			})
		  },
		});
	  },
	  copyText(text) {
		uni.setClipboardData({
		  data: text,
		  success: function () {
			uni.showToast({
			  title: '复制成功',
			  icon: 'none'
			});
		  }
		});
	  },
	  //获取招标详情
	  getDetail() {
		uni.showLoading({})
		let params = {
		  id: this.id,
		}
		this.$u.api.addressBookDetail(params).then(data => {
		  if (data.success) {
			this.result = data.data;
			uni.hideLoading()
		  } else {
			uni.navigateTo({
			  url: '/pages/login/login-account'
			})
		  }
		}).catch(err => {
		  this.$u.func.showToast({
			title: err,
		  })
		  uni.hideLoading()
		})
	  },
	}
  }
  </script>
  
  <style>
  .wrap {
	margin-top: 12px;
  }
  
  .list {
	background: #fff;
	padding: 12px 12px 20px 12px;
	border-bottom: 1px dashed #D8D8D8;
  }
  
  .left-title {
	border-left: 2px solid #4075FF;
	padding-left: 4px;
  }
  
  .warnTitle {
	color: red;
	font-size: 26rpx;
	letter-spacing: 2px;
	padding: 20rpx;
	text-align: center;
	background: #fff;
	border-bottom: 1px dashed #efefef;
  }
  </style>