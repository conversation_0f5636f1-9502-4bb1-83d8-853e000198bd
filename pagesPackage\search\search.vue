<template>
	<view class="u-page">
		<u-search placeholder="请输入品牌或材料设备名称" border="surround" :show-action="true" borderColor="rgb(230, 230, 230)"
			bgColor="#fff" shape="round" :clearabled="true" v-model="query.word" @custom="searchList"
			@search="searchList">
		</u-search>

		<view v-if="result.length > 0">
			<view class="news-list" v-for="item in result" :key="item.id">
				<view class="item">
					<view class="itemL">
						<view class="itemL_top">{{ item.companyName }}</view>
						<u-tag size="mini" v-if="item.ventureType!==-1"
							:text="getDictLabel(item.ventureType, ventureTypeOptions)" plain plainFill></u-tag>
						<view class="itemL_btm">{{ item.type }}</view>
					</view>
					<view class="itemR">
						<view class="itemR_top">
							<u-button v-if="item.wxLink" size="small" shape="circle" text="去 查 看" color="rgb(255, 0, 0)"
								@click="toH5(item.wxLink)"></u-button>
							<u-button v-else :plain="true" size="small" shape="circle" text="去 查 看"
								@click="refuse"></u-button>
						</view>
						<view class="itemR_btm">
							<u-icon name="eye" color="#2979ff" size="22" @click="viewDeatil(item.type)"></u-icon>
						</view>
					</view>
				</view>
			</view>
			<view class="no-more">
				<u-loadmore :status="status" dashed line :fontSize="12" />
			</view>
		</view>
		<view v-else style="padding-top: 40%;">
			<u-empty mode="list" text="暂无数据" />
		</view>

		<!-- 回到顶部 -->
		<view class="back-top-box">
			<u-back-top :scroll-top="scrollTop" />
		</view>

		<!-- 弹窗 -->
		<uni-popup ref="popup">
			<view class="popup-content">
				<view class="list">
					<view class="listItem" v-for="(item, index) in detail" :key="index">{{ index + 1 }}&nbsp;{{ item }}
					</view>
				</view>
				<view style="width: 32%;margin-left: 34%;margin-top: 6%;">
					<u-button type="primary" size="small" text="关 闭" style="width: 100%;"
						@click="this.$refs.popup.close();"></u-button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				pages: 1,
				status: 'loadmore',
				query: {
					current: 1,
					size: 10,
					word: ''
				},
				scrollTop: 0,
				result: [],
				detail: [],
				ventureTypeOptions: [], // 企业类型选项
			}
		},
		onLoad(options) {
			if (!uni.getStorageSync('accessToken')) {
				uni.navigateTo({
					url: '/pages/login/login-account'
				})
			} else {
				this.query.word = options.word
				this.getVentureTypeDictionary() // 获取企业类型字典
				this.pageSearch()
			}
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		onReachBottom() {
			this.status = 'loading';
			if (this.query.current >= this.pages) {
				this.status = 'nomore';
			} else {
				this.query.current++
				setTimeout(() => {
					this.pageSearch()
				}, 250)
			}
		},
		onShareAppMessage() {
			return {
				title: '供应商查询',
				path: '/pagesPackage/search/search?word=' + this.query.word,
			}
		},
		onShareTimeline() {
			return {
				title: '供应商查询',
				query: `word=${this.query.word}`,
			}
		},
		methods: {
			// 获取企业类型字典
			getVentureTypeDictionary() {
				this.$u.api.getDictionary({
					code: 'venture_type'
				}).then(res => {
					if (res.data && res.data.length > 0) {
						this.ventureTypeOptions = res.data.map(item => ({
							text: item.dictValue,
							value: item.dictKey
						}))
					}
				}).catch(err => {
					console.error('获取企业类型字典失败：', err)
				})
			},
			// 根据值获取字典标签
			getDictLabel(value, options) {
				if (!value || !options || !options.length) {
					return value;
				}
				const found = options.find(item => item.value == value);
				return found ? found.text : value;
			},
			viewDeatil(val) {
				this.$refs.popup.open()
				this.detail = val.split(',')
			},
			refuse() {
				uni.showToast({
					title: '该供应商暂未入库，请联系管理员要联系方式。',
					icon: 'none'
				});
			},
			toH5(val) {
				if (val.length > 0) {
					wx.openOfficialAccountArticle({
						url: val,
						success: function(res) {
							if (res.cancel) {
								uni.navigateTo({
									url: '/pages/webview/specified?url=' + encodeURIComponent(val)
								})
							}
						},
						fail: function(err) {
							console.log('打开失败', err);
						}
					})
				} else {
					uni.showToast({
						title: '此品牌暂未加入品牌库，联系工作人员咨询联系方式。',
						icon: 'none'
					});
				}
			},
			searchList() {
				this.pages = 1
				this.pageSearch()
			},
			pageSearch() {
				uni.showLoading({})
				if (this.scrollTop < 30) {
					this.query.current = 1
					this.result = []
					if (this.query.current >= this.pages) {
						this.status = 'nomore';
					}
				}
				this.$u.api.homePageSearch(this.query).then(data => {
					if (data.success) {
						if (data.data.records.length > 0) {
							this.pages = data.data.pages //总页数
							let arr = data.data.records
							this.result = this.result.concat(...arr)
						} else {
							this.status = 'nomore'
						}
						uni.hideLoading()
					} else {
						uni.showToast({
							title: data.msg,
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading()
					if (err.data.code === 401) {
						uni.showToast({
							title: '登录已过期，请重新登录！',
							duration: 3000,
							icon: 'none'
						})
						uni.navigateTo({
							url: '/pages/login/login-account'
						})
					} else {
						uni.showToast({
							title: err.data.msg,
							icon: 'none'
						});
						this.$u.func.showToast({
							title: err,
						})
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.news-list {
		height: 150rpx;
		margin-top: 30rpx;
		background: #fff;
		border-radius: 14rpx;

		.item {
			width: 98%;
			height: 100%;
			margin: auto;
			display: flex;
			box-sizing: border-box;
			padding: 3rpx;

			.itemL {
				width: 84%;
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: space-around;

				.itemL_top {
					width: 100%;
					// height: 60%;
					// line-height: 60rpx;
					color: #333;
					font-size: 30rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				.itemL_btm {
					width: 100%;
					// height: 40%;
					// line-height: 60rpx;
					color: #666;
					font-size: 24rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}

			.itemR {
				width: 16%;
				height: 100%;

				.itemR_top {
					width: 100%;
					height: 40%;
					margin-top: 20%;
				}

				.itemR_btm {
					width: 70%;
					height: 30%;
					margin-top: 10%;
					margin-left: 30%;
				}
			}
		}
	}

	.popup-content {
		padding: 20px;
		background-color: white;
		border-radius: 10px;
		box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
		text-align: center;

		.list {
			width: 100%;
			max-height: 590rpx;
			overflow-y: auto;
			color: #444;
			font-size: 26rpx;
		}

		.listItem {
			width: 480rpx;
			height: 60rpx;
			line-height: 60rpx;
			text-align: left;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
	}

	.u-page {
		margin: 20rpx;
	}

	::v-deep .u-tag {
		width: fit-content;
	}
</style>