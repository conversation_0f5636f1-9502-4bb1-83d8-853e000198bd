// 获取api目录所有js文件
const files = require.context('@/api', false, /\.js$/)
// 此处第二个参数vm，就是我们在页面使用的this，可以通过vm获取vuex等操作
const install = (Vue, vm) => {
	// 将各个定义的接口名称，统一放进对象挂载到vm.$u.api下(因为vm就是this，也即this.$u.api)
	// 自动将所有api挂载到vm.$u.api中
	vm.$u.api = {}
	files.keys().forEach(key => {
		// 获取文件的默认导出
		const apiDefault = files(key).default
		// 获取所有具名导出
		const apiModule = files(key)
		
		// 处理默认导出
		if (apiDefault) {
			for (let item in apiDefault) {
				vm.$u.api[item] = apiDefault[item]
			}
		}
		
		// 处理具名导出
		for (let item in apiModule) {
			// 排除default和__esModule等特殊属性
			if (item !== 'default' && !item.startsWith('__')) {
				vm.$u.api[item] = apiModule[item]
			}
		}
	})
}

export default {
	install
}
