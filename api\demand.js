import http from '@/http/api.js'

export const demandApp = (params) => {
  return http.request({
    url: '/hztech-eng/demandApp/page',
    method: 'GET',
    data: params
  })

}

export const myDemand = (params) => {
  return http.request({
    url: '/hztech-eng/demandApp/myDemand',
    method: 'GET',
    data: params
  })
}

export const getRegion = (params) => {
  return http.request({
    url: '/hztech-system/region/select',
    method: 'GET',
    data: params
  })
}

export const addRequirement = (params) => {
  return http.request({
    url: '/hztech-eng/demandApp/pushDemand',
    method: 'POST',
    header: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: params
  })
}

export const updateRequirement = (params) => {
  return http.request({
    url: '/hztech-eng/demandApp/update',
    method: 'POST',
    header: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: params
  })
}

export const getCategoryList = (current, size) => {
  return http.request({
    url: '/hztech-eng/demandCategory/list',
    method: 'GET',
    data: {
      current,
      size
    }
  })
}

export const getDemandMode = (params) => {
  return http.request({
    url: '/hztech-system/param/detail?paramKey=DEMAND_MODE',
    method: 'GET',
    data: params
  })
}

export const getDemandDetail = (params) => {
  return http.request({
    url: '/hztech-eng/demandApp/detail',
    method: 'GET',
    data: params
  })
}

export const getMessageList = (params) => {
  return http.request({
    url: '/hztech-eng/messageBoardApp/page',
    method: 'GET',
    data: params
  })
}

export const addComment = (params) => {
  return http.request({
    url: '/hztech-eng/messageBoardApp/leaveMessage',
    method: 'POST',
    header: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: params
  })
}

export const getMyDemand = (params) => {
  return http.request({
    url: '/hztech-eng/demandApp/myDemand',
    method: 'GET',
    data: params
  })
}

export const removeDemand = (ids) => {
  return http.request({
    url: '/hztech-eng/demandApp/remove?ids=' + ids,
    method: 'POST',
    header: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export const downDemand = (id) => {
  return http.request({
    url: '/hztech-eng/demandApp/down',
    method: 'POST',
    header: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: {
      id
    }
  })
}

export const renewDemand = (params) => {
  return http.request({
    url: '/hztech-eng/demandApp/renew',
    method: 'POST',
    header: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: params
  })
}

export const getDemandHistory = (params) => {
  return http.request({
    url: '/hztech-eng/demandApp/myDemandHistory',
    method: 'GET',
    data: params
  })
}

export const saveDemandHistory = (params) => {
  return http.request({
    url: '/hztech-eng/demandApp/saveDemandHistory',
    method: 'GET',
    data: params
  })
}

  export const getPackageList = (params) => {
    return http.request({
      url: '/hztech-eng/appPackage/page',
      method: 'GET',
      data: params
    })
  }

  export const getPackageDetail = (params) => {
    return http.request({
      url: '/hztech-eng/appPackage/detail',
      method: 'GET',
      data: params
    })
  }

  export const authUpdate = (params) => {
    return http.request({
      url: '/hztech-eng/app/authUpdate',
      method: 'POST',
      header: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data: params
    })
  }

  export const getNewsList = (params) => {
    return http.request({
      url: '/hztech-eng/newsbody/page',
      method: 'GET',
      data: params
    })
  }

  export const getNewsDetail = (params) => {
    return http.request({
      url: '/hztech-eng/newsbody/detail',
      method: 'GET',
      data: params
    })
  }

  export const allRead = (params) => {
    return http.request({
      url: '/hztech-eng/newsbody/allRead',
      method: 'GET',
      data: params
    })
  }

  export const removeNews = (ids) => {
    return http.request({
      url: '/hztech-eng/newsbody/remove?ids=' + ids,
      method: 'POST'
    })
  }

  export const isFinalDemand = (params) => {
    return http.request({
      url: '/hztech-eng/demand/isFinalDemand',
      method: 'POST',
      header: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data: params
    })
  }

  export const projectPage = (params) => {
    return http.request({
      url: '/hztech-eng/app/bid/projectContacts',
      method: 'GET',
      data: params
    })
  }

  export const addressBookDetail = (params) => {
    return http.request({
      url: '/hztech-eng/app/bid/projectContactsDetail',
      method: 'GET',
      data: params
    })
  }


  export const getEsDetailHtml = (params) => {
    return http.request({
      url: '/hztech-eng/app/bid/getEsDetailHtml',
      method: 'GET',
      data: params
    })
  }
