<template>
  <view class="wrap">
    <view class="tipinfo">以下信息仅供参考，请勿转载用于其它商业用途!</view>
    <view class="warnTitle">如若没有显示的信息请打开网址查看</view>
    <view class="list">
      <u-row customStyle="margin-bottom: 10px">
        <u-col span="12">
          <span class="left-title">项目名称：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px">
        <u-col span="12" customStyle="text-align: left;font-size:14px;color:#666">
          <span @click="copyText(result.name)">{{ result.name }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px">
        <u-col span="12">
          <span class="left-title">项目编号：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px">
        <u-col span="12" customStyle="text-align: left;font-size:14px;color:#666">
          <span @click="copyText(result.code)">{{ result.code }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px">
        <u-col span="12">
          <span class="left-title">项目所在地：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px">
        <u-col span="12" customStyle="text-align: left;font-size:14px;color:#666">
          <span @click="copyText(result.region)">{{ result.region }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px">
        <u-col span="12">
          <span class="left-title">采购方式：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px">
        <u-col span="12" customStyle="text-align: left;font-size:14px;color:#666">
          <span @click="copyText(result.type)">{{ result.type }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px">
        <u-col span="12">
          <span class="left-title">发布时间：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px">
        <u-col span="12" customStyle="text-align: left;font-size:14px;color:#666">
          <span @click="copyText(result.beginTime)">{{ result.beginTime ? result.beginTime.substring(0, 10) : '' }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px">
        <u-col span="12">
          <span class="left-title">网址链接：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px">
        <u-col span="12"
               customStyle="text-align: left;font-size:14px;color:#666;word-wrap:break-word;word-break:normal">
          <span>{{ result.url }}</span>
        </u-col>
      </u-row>
      <u-row>
        <u-col span="12" customStyle="text-align: left;font-size:14px;color:#666">
          <u-button text="复制链接" @click="copyUrl(result.url)"></u-button>
        </u-col>
      </u-row>
    </view>

    <view class="filebox" :class="fileResult.length > 1 ? 'preLit' : ''"
          v-for="(item,index) in fileResult" :key="index">
      <u-row customStyle="margin-bottom: 20rpx">
        <u-col span="12">
          <span>附件名称：</span>
        </u-col>
      </u-row>
      <u-row>
        <u-col span="12" customStyle="text-align: left">
          <span style="color: #555;">{{ item.name }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin:20rpx 0">
        <u-col span="12">
          <u-button type="primary" :plain="true" text="复制链接" @click="copyUrl(item.url)"/>
        </u-col>
      </u-row>
    </view>

    <view class="list">
      <mp-html lazy-load scroll-table selectable use-anchor :content="result.purchaseWay"></mp-html>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      id: '',
      name: '',
      result: {},
      fileResult: [],
    }
  },
  onLoad(options) {
    if (!uni.getStorageSync('accessToken')) {
      uni.navigateTo({
        url: '/pages/login/login-account'
      })
    } else {
      this.id = options.id
      this.name = options.name
      this.fileDetail()
      this.getDetail()
    }
  },
  onShareAppMessage() {
    return {
      title: this.name,
      path: '/pagesPackage/projectDetail/projectDetail?id=' + this.id + '&name=' + this.name,
    }
  },
  onShareTimeline() {
    return {
      title: this.name,
      query: `id=${this.id}&name=${this.name}`,
    }
  },
  methods: {
    //获取附件列表
    fileDetail() {
      uni.showLoading({})
      let params = {
        hostId: this.id,
        type: 10
      }
      this.$u.api.fileList(params).then(data => {
        if (data.success) {
          this.fileResult = data.data.records;
          uni.hideLoading()
        }
      }).catch(err => {
        this.$u.func.showToast({
          title: err,
        })
        uni.hideLoading()
      })
    },
    //获取补充公告详情
    getDetail() {
      uni.showLoading({})
      let params = {
        id: this.id
      }
      this.$u.api.supplementDetail(params).then(data => {
        if (data.success) {
          this.result = data.data;
          uni.hideLoading()
        }
      }).catch(err => {
        this.$u.func.showToast({
          title: err,
        })
        uni.hideLoading()
      })
    },
    //复制文本
    copyText(text) {
      uni.setClipboardData({
        data: text,
        success: function () {
          uni.showToast({
            title: '复制成功',
            icon: 'none'
          });
        }
      });
    },
    //复制链接
    copyUrl(url) {
      uni.setClipboardData({
        data: url,
        success: function () {
          uni.showToast({
            title: '复制成功',
            icon: 'none'
          });
        }
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.wrap {
  margin-top: 6rpx;
  padding: 10px;
  min-height: 100vh;
}

.list {
  background: #fff;
  padding: 12px 12px 20px 12px;
  border-bottom: 1px dashed #D8D8D8;
}

.filebox {
  padding: 10rpx;
  margin: 20px 0;
  background: #fff;
}

.warnTitle {
  color: red;
  font-size: 26rpx;
  letter-spacing: 2px;
  padding: 20rpx;
  text-align: center;
  background: #fff;
  border-bottom: 1px dashed #efefef;
}
</style>