<template>
	<view class="u-page">
		<view v-if="result.length > 0" style="margin: 24rpx;">
			<view class="news-list" v-for="item in result" :key="item.id">
				<view @click="toDetail(item)" hover-class="none" class="news-item">
					<view class="item-header">
						<view class="item-date">{{ item.createTime.split(' ')[0] }}</view>
						<view class="item-status"
							:class="{
								'status-final': item.finalStatus === 1,
								'status-published': item.finalStatus !== 1 && item.putStatus === 1,
								'status-offline': item.finalStatus !== 1 && item.putStatus !== 1
							}">
							{{ item.finalStatus===1?'已完成':item.putStatus===1?'已发布':'已下架' }}
						</view>
					</view>
					<view class="item-content-box">
						<view v-if="item.demandImg[0].startsWith('http')" class="item-images">
							<image :src="item.demandImg[0]" class="item-image"></image>
						</view>
						<view class="item-content-right">
							<view class="item-content">
								{{ item.demandTitle  }}
							</view>
							<view class="item-footer">
								<view class="read-info">
									<text class="read-text">已读 <text class="read-count">{{ item.readCount }}</text>
									</text>
									<text>未读 <text class="unread-count">{{ item.noReadCount }}</text></text>
								</view>
								<image class="delete-btn" @click.stop="deleteDemand(item.id)"
									src="@/static/icon/del.png"></image>
							</view>
						</view>
					</view>
					<view class="action-btns">
						<view @click.stop="showDatePicker(item)" v-if="item.putStatus===1" class="btn btn-renew">有效续期
						</view>
						<view @click.stop="downDemand(item.id)" v-if="item.putStatus===1" class="btn btn-offline">下架
						</view>
					</view>
				</view>
			</view>
			<view class="no-more" v-if="result.length != 0">
				<u-loadmore :status="status" dashed line :fontSize="12" />
			</view>
		</view>

		<view style="padding-top:30%;" v-else>
			<u-empty mode="list" text="暂无数据">
			</u-empty>
		</view>
		<!--    回到顶部 -->
		<view class="back-top-box">
			<u-back-top :scroll-top="scrollTop"></u-back-top>
		</view>

		<!-- 日期选择器弹窗 -->
		<uni-popup ref="datePopup" type="center">
			<view class="date-picker-box">
				<view class="date-picker-title">请选择结束日期</view>
				<picker mode="date" :value="expireDate" :start="startDate" @change="onDateChange">
					<view class="date-value">{{ expireDate || '请选择日期' }}</view>
				</picker>
				<view class="date-picker-btns">
					<button class="date-btn btn-cancel" @click="cancelDatePicker">取消</button>
					<button class="date-btn btn-confirm" @click="confirmDatePicker">确定</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				scrollTop: 0,
				pages: 1,
				status: 'loadmore',
				query: {
					current: 1,
					size: 10,
					categoryId: '',
				},
				result: [],
				categoryList: [],
				filterVisible: false, // 控制筛选弹窗的显示
				filterData: [], // 初始化为空数组

				// 日期选择相关
				currentDemandId: null,
				expireDate: '',
				startDate: ''
			}
		},
		onShow() {
			if (!uni.getStorageSync('accessToken')) {
				uni.navigateTo({
					url: '/pages/login/login-account'
				})
			} else {
				if (this.query.current >= this.pages) {
					this.status = 'nomore';
				}
				this.pageSearch();
				this.getCategoryList();
			}
		},
		onReachBottom() {
			this.status = 'loading';
			if (this.query.current >= this.pages) {
				this.status = 'nomore';
			} else {
				this.query.current++
				setTimeout(() => {
					this.pageSearch()
				}, 200)
			}
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		onShareAppMessage() {
			return {
				title: '我的需求',
				path: '/pagesPackage/my/myDemand',
			}
		},
		onShareTimeline() {
			return {
				title: '我的需求',
				path: '/pagesPackage/my/myDemand',
			}
		},
		methods: {
			showDatePicker(item) {
				this.currentDemandId = item.id;

				this.startDate = item.expireTime;

				// 默认选择一个月后的日期
				const nextMonth = new Date(item.expireTime);
				nextMonth.setMonth(nextMonth.getMonth() + 1);
				const nextYear = nextMonth.getFullYear();
				const nextMonthStr = String(nextMonth.getMonth() + 1).padStart(2, '0');
				const nextDay = String(nextMonth.getDate()).padStart(2, '0');
				this.expireDate = `${nextYear}-${nextMonthStr}-${nextDay}`;

				this.$refs.datePopup.open();
			},
			onDateChange(e) {
				this.expireDate = e.detail.value;
			},
			cancelDatePicker() {
				this.$refs.datePopup.close();
			},
			confirmDatePicker() {
				if (!this.expireDate) {
					uni.showToast({
						title: '请选择日期',
						icon: 'none'
					});
					return;
				}

				this.$refs.datePopup.close();
				this.renewDemand(this.currentDemandId, this.expireDate);
			},

			renewDemand(id, date) {
				const params = {
					id,
					expireTime: date + ' 00:00:00'
				}
				this.$u.api.renewDemand(params).then(data => {
					if (data.success) {
						this.pageSearch()
						uni.showToast({
							title: '续期成功',
							icon: 'success'
						})
					}
				})
			},
			deleteDemand(id) {
				uni.showModal({
					title: '提示',
					content: '确定删除该需求吗？',
					success: (res) => {
						if (res.confirm) {
							this.$u.api.removeDemand(id).then(data => {
								if (data.success) {
									this.query.current = 1;
									this.pageSearch()
									uni.showToast({
										title: '删除成功',
										icon: 'success'
									})
								}
							}).catch(err => {
								uni.showToast({
									title: err.data.msg,
									icon: 'none'
								})
							})
						}
					}
				})
			},
			downDemand(id) {
				uni.showModal({
					title: '提示',
					content: '确定下架该需求吗？',
					success: (res) => {
						if (res.confirm) {
							this.$u.api.downDemand(id).then(data => {
								if (data.success) {
									this.query.current = 1;
									this.pageSearch()
									uni.showToast({
										title: '下架成功',
										icon: 'success'
									})
								}
							}).catch(err => {
								uni.showToast({
									title: err.data.msg,
									icon: 'none'
								})
							})
						}
					}
				})
			},
			addDemand() {
				this.$u.func.route('/pagesPackage/requirementRelease/addRequirement')
			},
			getCategoryList() {
				this.$u.api.getCategoryList(1, -1).then(data => {
					this.categoryList = data.data.records.filter(item => item.enableStatus)
					this.categoryList.unshift({
						id: '',
						categoryName: '全部'
					})
				})
			},
			reset() {
				this.query = {
					current: 1,
					size: 10,
				}
				this.result = []
				this.pageSearch()
			},
			toDetail(e) {
				this.$u.func.route(`/pagesPackage/requirementRelease/requirementDetail?id=${e.id}&openType=my`)
			},
			selectCategory(e) {
				this.query.categoryId = e.id
				this.query.current = 1
				this.result = []
				this.pageSearch()
			},
			pageSearch() {
				uni.showLoading({})
				this.$u.api.getMyDemand(this.query).then(data => {
					if (data.success) {
						this.pages = data.data.pages
						let arr = data.data.records.map(item => {
							item.demandImg = item.demandImg.split(',')
							return item
						})
						if (arr.length == 0) {
							this.result = []
						} else {
							if (this.query.current === 1) {
								this.result = [...arr]
							} else {
								this.result = this.result.concat(...arr)
							}
						}
						uni.hideLoading()
					} else {
						uni.showToast({
							title: data.data.msg,
							icon: 'none'
						});
					}
				}).catch(err => {
					console.log(err)
					uni.hideLoading()
					if (err.data.code === 401) {
						uni.showToast({
							title: '登录已过期，请重新登录！',
							duration: 3000,
							icon: 'none'
						})
						uni.navigateTo({
							url: '/pages/login/login-account'
						})
					} else {
						uni.showToast({
							title: err.data.msg,
							icon: 'none'
						});
					}
				})
			},
			handleFilterConfirm(selectedItems) {
				// 处理筛选数据
				console.log('筛选条件:', selectedItems);
				this.query = {
					current: this.query.current,
					size: this.query.size,
					categoryId: this.query.categoryId,
					...selectedItems
				}

				this.filterVisible = false;
				// 根据筛选条件重新查询数据
				this.query.current = 1;
				this.result = [];
				this.pageSearch();
			}
		}
	}
</script>

<style lang="scss" scoped>
	view {
		box-sizing: border-box;
	}

	.search-container {
		background: #fff;
		padding: 20px 10px
	}

	.filter-header {
		display: flex;
		justify-content: flex-end;
	}

	.filter-more {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		color: #585b61;

		text {
			margin-right: 4rpx;
		}
	}

	.search-top {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.search-pick {
		font-size: 24rpx;
	}

	.news-list {
		margin-top: 30rpx;

		.news-item {
			background: #fff;
			border-radius: 8rpx;
			padding: 24rpx;
			margin-bottom: 20rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

			.item-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;

				.item-date {
					font-family: Source Han Sans;
					font-size: 26rpx;
					font-weight: normal;
					line-height: normal;
					letter-spacing: normal;
					color: #333333;
				}

				.item-status {
					font-family: Source Han Sans;
					font-size: 26rpx;
					font-weight: 600;
					line-height: normal;
					letter-spacing: normal;
				}

				.status-final {
					color: #4d78cc;
				}

				.status-published {
					color: #12D500;
				}

				.status-offline {
					color: #666666;
				}

			}

			.delete-btn {
				width: 34rpx;
				height: 36rpx;
			}

			.item-content {
				width: 100%;
				font-size: 32rpx;
				font-weight: 500;
				color: #333;
				line-height: 1.5;
				margin-bottom: 20rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				word-break: break-all;
			}

			.item-images {
				width: 168rpx;
				height: 168rpx;
				margin-right: 24rpx;
				flex-shrink: 0;

				.item-image {
					width: 100%;
					height: 100%;
					border-radius: 8rpx;
				}
			}

			.item-content-box {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.item-content-right {
					flex: 1;
					height: 168rpx;
					padding: 16rpx 0 0 0;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					box-sizing: border-box;
					width: 0;
					min-width: 0;
					overflow: hidden;
				}
			}

			.item-footer {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.read-info {
					font-size: 24rpx;
					color: #333333;
					display: flex;
					align-items: center;


					.read-text {
						margin-right: 24rpx;
						display: flex;
						align-items: center;
					}

					.read-count {
						color: #12D500;
						margin-left: 8rpx;
					}

					.unread-count {
						color: #333333;
						margin-left: 8rpx;
					}
				}
			}
		}
	}

	.action-btns {
		display: flex;
		justify-content: flex-end;
		margin-top: 24rpx;

		.btn {
			width: 162rpx;
			height: 64rpx;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-family: Source Han Sans;
			font-size: 26rpx;
			font-weight: normal;
			line-height: normal;
			letter-spacing: normal;
			margin-left: 40rpx;
		}

		.btn-renew {
			background: #4075FF;
			color: #fff;
		}

		.btn-offline {
			background-color: #FFFFFF;
			color: #333333;
			border: 2rpx solid #4075FF;
		}
	}

	.uni-swiper-tab {
		white-space: nowrap;
	}

	.model_scrollx {
		justify-content: space-between;
		height: 45px;
		align-items: center;
	}

	.scrollx_items {
		text-align: center;
		display: inline-block;
		min-width: 100rpx;
		box-sizing: border-box;
		margin-right: 20rpx;
		margin-top: 3px;
	}

	.scrollx_items:last-child {
		margin-right: 30rpx;
	}

	.scrollx_items image {
		width: 70rpx;
		height: 66rpx;
	}

	.tgyx_title {
		font-size: 28rpx;
		color: #333333;
		margin-top: 30rpx;
	}

	.tgyx_desc {
		font-size: 24rpx;
		margin-top: 10rpx;
	}

	.line2 {
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	.active {
		color: #4075FF;
		background: #F1F5FF;
		border: 1px solid #F1F5FF !important
	}

	.item-tabs {
		margin-top: 10px;
	}

	.item-tabs {
		border-radius: 8rpx;
		border: 1px solid rgb(230, 230, 230);
		height: 54rpx;
		display: flex;
		font-size: 28rpx;
		align-items: center;
		justify-content: center;
		padding: 0 10rpx;
	}

	.list {
		background: #F7F7FA;
		padding: 10rpx;
		margin-bottom: 22rpx;
	}

	.flex-1 {
		display: flex;
		flex: 1;
		align-items: center;
		justify-content: center;
	}

	.region {
		color: #0bb9c8;
		position: absolute;
		left: 0;
		bottom: 0;
		display: flex;
		font-size: 24rpx;
	}

	.number {
		position: absolute;
		bottom: 36rpx;
		left: 0;
		width: 180rpx;
		height: 32rpx;
		line-height: 32rpx;
		font-size: 24rpx;
		text-align: center;
		border-radius: 2px;
		font-weight: 500;
	}

	.sum {
		position: absolute;
		bottom: 36rpx;
		right: 0;
		width: 200rpx;
		height: 34rpx;
		line-height: 34rpx;
		font-size: 24rpx;
		text-align: right;
		border-radius: 2px;
		font-weight: 500;
		color: #000000;
	}

	.add-box {
		position: fixed;
		bottom: 188rpx;
		right: 120rpx;
	}

	.date-picker-box {
		width: 600rpx;
		background-color: #fff;
		border-radius: 16rpx;
		padding: 40rpx 30rpx;
	}

	.date-picker-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		text-align: center;
		margin-bottom: 30rpx;
	}

	.date-value {
		height: 80rpx;
		line-height: 80rpx;
		border: 1px solid #e6e6e6;
		border-radius: 8rpx;
		padding: 0 20rpx;
		text-align: center;
		font-size: 28rpx;
		color: #333;
	}

	.date-picker-btns {
		display: flex;
		justify-content: space-between;
		margin-top: 40rpx;
	}

	.date-btn {
		flex: 1;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 8rpx;
		font-size: 28rpx;
		margin: 0 20rpx;
	}

	.btn-cancel {
		background-color: #f7f7f7;
		color: #333;
	}

	.btn-confirm {
		background-color: #4075FF;
		color: #fff;
	}
</style>