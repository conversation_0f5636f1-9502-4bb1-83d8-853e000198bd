<template>
	<view class="container">
		<view class="form-container">
			<!-- <view class="form-avatar">
				<image :src="userInfo.avatar || '/static/images/tabbar/user_selected.png'" mode="widthFix"></image>
			</view> -->
			<uni-forms ref="customForm" :rules="customRules" :modelValue="FormData" :label-width="90">
				<!-- <uni-forms-item label="姓名" name="publisherName" :required="isFieldRequired('publisherName')"
					v-if="!hiddenFields.publisherName">
					<view class="uni-input">{{ FormData.publisherName }}</view>
				</uni-forms-item>
				<uni-forms-item label="手机号" name="phone" :required="isFieldRequired('phone')"
					v-if="!hiddenFields.phone">
					<view class="uni-input">{{ FormData.phone }}</view>
				</uni-forms-item>
				<uni-forms-item label="单位名称" name="unitName" :required="isFieldRequired('unitName')"
					v-if="!hiddenFields.unitName">
					<view class="uni-input">{{ FormData.unitName }}</view>
				</uni-forms-item>
				<uni-forms-item label="发布者属性" name="publisherType" :required="isFieldRequired('publisherType')"
					v-if="!hiddenFields.publisherType">
					<view class="uni-input">{{ FormData.publisherTypeName }}</view>
				</uni-forms-item>
				<uni-forms-item label="岗位职务" name="jobTitle" :required="isFieldRequired('jobTitle')"
					v-if="!hiddenFields.jobTitle">
					<view class="uni-input">{{ FormData.jobTitleName }}</view>
				</uni-forms-item> -->
				<uni-forms-item label="有效时间" name="expireTime" :required="isFieldRequired('expireTime')"
					v-if="!hiddenFields.expireTime">
					<picker
						style=" height: 100%;display: flex;align-items: center;border-bottom: 2rpx solid #e5e5e5;padding-left: 20rpx;"
						mode="date" :value="FormData.expireTime" :start="getTodayDate()" @change="bindDateChange">
						<view>{{ FormData.expireTime }}</view>
					</picker>
				</uni-forms-item>
				<!-- <view class="tip">*以上信息为必填项，信息不公开，仅发布者与平台可见</view> -->
				<uni-forms-item label="需求类别" name="categoryId" required v-if="!hiddenFields.categoryId">
					<uni-data-select v-model="FormData.categoryId" :localdata="categoryOptions"></uni-data-select>
				</uni-forms-item>
				<uni-forms-item label="需求主题" name="demandTitle" required v-if="!hiddenFields.demandTitle">
					<uni-easyinput v-model="FormData.demandTitle" maxlength="64" placeholder="请输入需求主题" />
				</uni-forms-item>
				<uni-forms-item label="查找对象" name="targetObject" :required="isFieldRequired('targetObject')"
					v-if="!hiddenFields.targetObject">
					<uni-easyinput v-model="FormData.targetObject" maxlength="64" placeholder="请输入查找对象" />
				</uni-forms-item>
				<uni-forms-item label="对象属性" name="objectProperty" :required="isFieldRequired('objectProperty')"
					v-if="!hiddenFields.objectProperty">
					<!-- <uni-easyinput v-model="FormData.objectProperty" maxlength="64" placeholder="请输入对象属性" /> -->
					<uni-data-select v-model="FormData.objectProperty"
						:localdata="publisherAttrOptions"></uni-data-select>
				</uni-forms-item>
				<uni-forms-item v-if="currentCategoryName === '材料设备供应商' && !hiddenFields.major" label="专业" name="major"
					:required="isFieldRequired('major')">
					<uni-data-select v-model="FormData.major" :localdata="majorOptions"></uni-data-select>
				</uni-forms-item>
				<uni-forms-item label="项目地点" name="projectLocation" :required="isFieldRequired('projectLocation')"
					v-if="!hiddenFields.projectLocation">
					<view class="location-picker">
						<uni-data-select v-model="FormData.provinceCode" :localdata="provinceOptions"></uni-data-select>
						<uni-data-select v-model="FormData.cityCode" :localdata="cityOptions"></uni-data-select>
						<uni-data-select v-model="FormData.districtCode" :localdata="districtOptions"></uni-data-select>
					</view>
					<uni-easyinput v-model="FormData.projectLocation" maxlength="64" placeholder="请输入详细地址" />
				</uni-forms-item>
				<!-- <uni-forms-item label="公司名称" name="companyName" :required="isFieldRequired('companyName')"
					v-if="!hiddenFields.companyName">
					<uni-easyinput v-model="FormData.companyName" placeholder="请输入公司名称" />
				</uni-forms-item> -->
				<uni-forms-item label="需求数量" name="quantity" :required="isFieldRequired('quantity')"
					v-if="!hiddenFields.quantity">
					<uni-easyinput v-model="FormData.quantity" maxlength="10" placeholder="请输入需求数量" />
				</uni-forms-item>
				<uni-forms-item label="材料设备名称+品牌名称" name="brandName" :required="isFieldRequired('brandName')"
					v-if="!hiddenFields.brandName">
					<uni-easyinput v-model="FormData.brandName" maxlength="64" placeholder="请输入材料设备名称+品牌名称" />
				</uni-forms-item>
				<uni-forms-item label="微信文章链接" name="wxPublicRdUrl" :required="isFieldRequired('wxPublicRdUrl')"
					v-if="!hiddenFields.wxPublicRdUrl">
					<uni-easyinput v-model="FormData.wxPublicRdUrl" placeholder="请输入微信文章链接" />
				</uni-forms-item>
				<uni-forms-item label="项目图片" name="demandImg" :required="isFieldRequired('demandImg')"
					v-if="!hiddenFields.demandImg">
					<Urlimg :size="120" :height="120" :imgList="FormData.demandImg" :max="5" @getImage="uploadFile">
					</Urlimg>
				</uni-forms-item>
				<uni-forms-item v-if="currentCategoryName === '工程资质买卖和办理' && !hiddenFields.qualification" label="工程资质"
					:required="isFieldRequired('qualification')" name="qualification">
					<uni-data-select v-model="FormData.qualification"
						:localdata="qualificationOptions"></uni-data-select>
				</uni-forms-item>
				<uni-forms-item v-if="currentCategoryName === '工程资质买卖和办理' && !hiddenFields.qualificationRemark"
					label="工程资质描述" :required="isFieldRequired('qualificationRemark')" name="qualificationRemark">
					<uni-easyinput v-model="FormData.qualificationRemark" type="textarea" placeholder="文字描述"
						maxlength="200" />
				</uni-forms-item>
				<uni-forms-item v-if="currentCategoryName === '证书/职称' && !hiddenFields.certificateTitle" label="证书/职称"
					:required="isFieldRequired('certificateTitle')" name="certificateTitle">
					<uni-data-select v-model="FormData.certificateTitle"
						:localdata="certificateOptions"></uni-data-select>
				</uni-forms-item>
				<uni-forms-item v-if="currentCategoryName === '证书/职称' && !hiddenFields.ctremark" label="证书/职称描述"
					:required="isFieldRequired('ctremark')" name="ctremark">
					<uni-easyinput v-model="FormData.ctremark" type="textarea" placeholder="文字描述" maxlength="200" />
				</uni-forms-item>
				<uni-forms-item label="需求描述" name="description" :required="isFieldRequired('description')"
					v-if="!hiddenFields.description">
					<uni-easyinput v-model="FormData.description" type="textarea" placeholder="文字描述" maxlength="200" />
				</uni-forms-item>
				<uni-forms-item label="其他要求" name="demandRemark" :required="isFieldRequired('demandRemark')"
					v-if="!hiddenFields.demandRemark">
					<uni-easyinput v-model="FormData.demandRemark" type="textarea" placeholder="文字描述" maxlength="200" />
				</uni-forms-item>
				<view class="tip-row">*请详细填写描述信息，方便查找对接</view>
				<view class="tip-row">*禁止发布虚假、违规、违反广告法(投资理财、信贷)等内容</view>
				<view class="tip-row">*如有违规，平台有权直接删除并封号处理</view>
			</uni-forms>
		</view>
		<view class="submit-box">
			<view class="submit-text" @click="submit('customForm')">提交</view>
		</view>
	</view>
</template>
<script>
	import Urlimg from "../components/urlImg.vue"

	export default {
		components: {
			Urlimg
		},
		data() {
			return {
				publisherAttrOptions: [],
				majorOptions: [],
				jobTitleOptions: [],
				qualificationOptions: [],
				certificateOptions: [],
				categoryOptions: [],
				provinceOptions: [],
				cityOptions: [],
				districtOptions: [],
				isInitialLoad: true,
				// 字段隐藏控制
				hiddenFields: {},
				// 自定义表单数据
				FormData: {
					userId: '',
					publisherName: '',
					phone: '',
					unitName: '',
					publisherType: '',
					jobTitle: '',
					expireTime: '',
					categoryId: '',
					demandTitle: '',
					targetObject: '',
					objectProperty: '',
					major: '',
					provinceCode: '',
					provinceName: '',
					cityCode: '',
					cityName: '',
					districtCode: '',
					districtName: '',
					projectLocation: '',
					quantity: '',
					brandName: '',
					demandImg: [],
					description: '',
					demandRemark: '',
					qualification: '',
					qualificationRemark: '',
					certificateTitle: '',
					ctremark: ''
				},
				// 自定义表单校验规则
				customRules: {
					categoryId: {
						rules: [{
							required: true,
							errorMessage: '需求类别不能为空'
						}]
					},
					demandTitle: {
						rules: [{
							required: true,
							errorMessage: '需求主题不能为空'
						}]
					},
					publisherName: {
						rules: [{
							required: true,
							errorMessage: '姓名不能为空'
						}]
					},
					phone: {
						rules: [{
							required: true,
							errorMessage: '手机号不能为空'
						}]
					},
					unitName: {
						rules: [{
							required: true,
							errorMessage: '单位名称不能为空'
						}]
					},
					publisherType: {
						rules: [{
							required: true,
							errorMessage: '发布者属性不能为空'
						}]
					},

				},
				openType: ''
			}
		},
		computed: {
			currentCategoryName() {
				if (!this.FormData.categoryId || !this.categoryOptions.length) return '';
				const category = this.categoryOptions.find(item => item.value === this.FormData.categoryId);
				return category ? category.text : '';
			}
		},
		watch: {
			'FormData.provinceCode': {
				handler(newValue) {
					if (newValue) {
						// 根据选择的省份获取城市列表
						this.$u.api.getRegion({
							code: newValue
						}).then(res => {
							this.cityOptions = res.data.map(item => ({
								text: item.name,
								value: item.code
							}))

							// 设置省份名称
							const province = this.provinceOptions.find(item => item.value === newValue)
							if (province) {
								this.FormData.provinceName = province.text
							}

							// 仅在非初始加载时清空区域选择
							if (!this.isInitialLoad) {
								this.FormData.cityCode = ''
								this.FormData.cityName = ''
								this.FormData.districtCode = ''
								this.FormData.districtName = ''
								this.districtOptions = []
							}
						})
					} else {
						if (!this.isInitialLoad) {
							this.FormData.cityCode = ''
							this.FormData.cityName = ''
							this.FormData.districtCode = ''
							this.FormData.districtName = ''
						}
						this.cityOptions = []
						this.districtOptions = []
					}
				},
				immediate: false
			},
			'FormData.cityCode': {
				handler(newValue) {
					if (newValue) {
						// 根据选择的城市获取区域列表
						this.$u.api.getRegion({
							code: newValue
						}).then(res => {
							this.districtOptions = res.data.map(item => ({
								text: item.name,
								value: item.code
							}))

							// 设置城市名称
							const city = this.cityOptions.find(item => item.value === newValue)
							if (city) {
								this.FormData.cityName = city.text
							}

							// 仅在非初始加载时清空区域选择
							if (!this.isInitialLoad) {
								this.FormData.districtCode = ''
								this.FormData.districtName = ''
							}
						})
					} else {
						if (!this.isInitialLoad) {
							this.FormData.districtCode = ''
							this.FormData.districtName = ''
						}
						this.districtOptions = []
					}
				},
				immediate: false
			},
			'FormData.districtCode': {
				handler(newValue) {
					if (newValue) {
						// 设置区域名称
						const district = this.districtOptions.find(item => item.value === newValue)
						if (district) {
							this.FormData.districtName = district.text
						}
					} else {
						this.FormData.districtCode = ''
						this.FormData.districtName = ''
						this.districtOptions = []
					}
				},
				immediate: false
			}
		},
		onLoad(options) {
			if (options.formData) {
				this.FormData = JSON.parse(options.formData)
				this.FormData.demandImg = this.FormData.demandImg.split(',')
				this.openType = 'edit'
			}
			this.getUserId()
			this.FormData.expireTime = this.getNextMonthDate()
		},
		async onReady() {
			await this.getFilterData()
			this.getCategoryOptions()
			await this.getProvinceOptions()

			// 如果是编辑模式，需要加载城市和区域数据
			if (this.openType === 'edit' && this.FormData.provinceCode) {
				await this.loadCityOptions()
				if (this.FormData.cityCode) {
					await this.loadDistrictOptions()
				}
			}

			this.isInitialLoad = false // 初始化完成后设置标记为false
			this.GenerateRule()
			this.Feedback()
		},
		methods: {
			Feedback() {
				this.FormData.publisherName = this.userInfo.userName
				this.FormData.phone = this.userInfo.userPhone
				this.FormData.unitName = this.userInfo.companyName
				this.FormData.publisherType = this.userInfo.publisherType
				this.FormData.jobTitle = this.userInfo.companyPost
			},
			GenerateRule() {
				this.$u.api.getDemandMode().then(res => {
					const fieldList = ["需求标题", "对象属性", "专业", "岗位", "工程资质", "证书", "项目地点", "公司名称", "材料设备名称+品牌名称",
						"需求数量", "其他要求", "项目相关图片", "文字描述", "微信文章链接"
					];
					const data = res.data.paramValue ? JSON.parse(res.data.paramValue) : []

					const fieldMapping = {
						"需求标题": {
							field: "demandTitle",
							message: "需求主题不能为空"
						},
						"对象属性": {
							field: "objectProperty",
							message: "请选择对象属性"
						},
						"专业": {
							field: "major",
							message: "请选择专业"
						},
						"岗位": {
							field: "jobTitle",
							message: "请选择岗位职务"
						},
						"工程资质": {
							field: "qualification",
							message: "工程资质不能为空"
						},
						"证书": {
							field: "certificateTitle",
							message: "证书/职称不能为空"
						},
						"项目地点": {
							field: "projectLocation",
							message: "详细地址不能为空"
						},
						// "公司名称": {
						// 	field: "companyName",
						// 	message: "公司名称不能为空"
						// },
						"材料设备名称+品牌名称": {
							field: "brandName",
							message: "请输入材料设备名称+品牌名称"
						},
						"需求数量": {
							field: "quantity",
							message: "需求数量不能为空"
						},
						"其他要求": {
							field: "demandRemark",
							message: "其他要求不能为空"
						},
						"项目相关图片": {
							field: "demandImg",
							message: "请上传项目图片"
						},
						"文字描述": {
							field: "description",
							message: "需求描述不能为空"
						},
						"微信文章链接": {
							field: "wxPublicRdUrl",
							message: "微信文章链接不能为空"
						}
					};

					// 初始化hiddenFields
					this.hiddenFields = {};

					// 动态设置必填规则和字段隐藏
					if (data && data.length > 0) {
						const newRules = {
							...this.customRules
						};

						fieldList.forEach((fieldName, index) => {
							if (data[index] && data[index][0] !== undefined) {
								const required = data[index][0] === 1;
								const mapping = fieldMapping[fieldName];

								// 处理字段隐藏逻辑
								if (data[index][3]) {
									if (mapping && mapping.field) {
										this.hiddenFields[mapping.field] = true;
									}
								} else if (mapping && mapping.field) {
									this.hiddenFields[mapping.field] = false;
								}

								if (mapping && mapping.field) {
									// 如果字段存在于映射中，更新规则
									if (required) {
										// 设置为必填
										if (!newRules[mapping.field]) {
											newRules[mapping.field] = {
												rules: []
											};
										}

										const requiredRule = newRules[mapping.field].rules.find(rule =>
											rule.required !== undefined);
										if (requiredRule) {
											requiredRule.required = true;
										} else {
											newRules[mapping.field].rules.push({
												required: true,
												errorMessage: mapping.message
											});
										}
									} else {
										// 设置为非必填
										if (newRules[mapping.field] && newRules[mapping.field].rules) {
											const ruleIndex = newRules[mapping.field].rules.findIndex(
												rule => rule.required !== undefined);
											if (ruleIndex !== -1) {
												newRules[mapping.field].rules.splice(ruleIndex, 1);
											}
										}
									}
								}
							}
						});

						// 更新表单规则
						console.log(newRules);

						this.customRules = newRules;
						this.$refs.customForm.setRules(this.customRules);
					}
				})
			},
			uploadFile(e) {
				console.log(e);
				// this.imgList = e
				const iMgList = e
				this.FormData.demandImg = iMgList
				console.log('this.imgList ', this.FormData.demandImg)
				if (JSON.stringify(e) === "[]") {
					this.FormData.demandImg = ''
				} else {
					this.FormData.demandImg = e.join(',')
				}
			},
			bindDateChange(e) {
				this.FormData.expireTime = e.target.value
			},
			getUserId() {
				// 获取当前用户ID
				const userInfo = uni.getStorageSync('userInfo')
				if (userInfo) {
					this.FormData.userId = userInfo.id
				}
			},
			getTodayDate() {
				const date = new Date()
				const year = date.getFullYear()
				const month = (date.getMonth() + 1).toString().padStart(2, '0')
				const day = date.getDate().toString().padStart(2, '0')
				return `${year}-${month}-${day}`
			},
			getCategoryOptions() {
				this.$u.api.getCategoryList(1, -1).then(data => {
					this.categoryOptions = data.data.records.filter(item => item.enableStatus).map(item => ({
						text: item.categoryName,
						value: item.id
					}))
				})
			},
			getProvinceOptions() {
				// 获取省份数据
				this.$u.api.getRegion().then(res => {
					this.provinceOptions = res.data.map(item => ({
						text: item.name,
						value: item.code
					}))
				})
			},
			async getFilterData() {
				// 字典类型与对应选项的映射
				const dictMapping = {
					'publisher_type': 'publisherAttrOptions',
					'major': 'majorOptions',
					'job_title': 'jobTitleOptions',
					'engineering_qualification': 'qualificationOptions',
					'certificate': 'certificateOptions',
				};

				try {
					// 创建并行请求的Promise数组
					const promises = Object.entries(dictMapping).map(async ([code, optionName]) => {
						try {
							const res = await this.$u.api.getDictionaryBiz({
								code
							});
							this[optionName] = res.data.map(item => ({
								text: item.dictValue,
								value: item.dictKey
							}));
						} catch (err) {
							console.error(`获取${code}字典数据失败:`, err);
						}
					});

					// 等待所有请求完成
					await Promise.all(promises);
				} catch (error) {
					console.error('获取字典数据失败:', error);
				}
			},
			submit(ref) {
				// 提交前移除隐藏字段的验证规则
				for (const field in this.hiddenFields) {
					if (this.hiddenFields[field] && this.customRules[field]) {
						// 临时保存规则
						const tempRules = {
							...this.customRules[field]
						};
						// 移除必填规则
						this.customRules[field] = {
							rules: tempRules.rules.filter(rule => rule.required === undefined)
						};
					}
				}

				// 刷新表单规则
				this.$refs[ref].setRules(this.customRules);

				this.$refs[ref].validate().then(res => {
					console.log('success', res);

					if (this.FormData.projectLocation) {
						if (!this.FormData.districtCode) {
							uni.showToast({
								title: '请选择项目地点省市区',
								icon: 'none'
							})
							return
						}
					}

					// 设置区域名称
					if (this.FormData.districtCode) {
						const district = this.districtOptions.find(item => item.value === this.FormData
							.districtCode)
						if (district) {
							this.FormData.districtName = district.text
						}
					}
					this.FormData.userId = this.userInfo.user_id
					if (!this.FormData.expireTime.includes(' 00:00:00')) {
						this.FormData.expireTime = this.FormData.expireTime + ' 00:00:00'
					}

					if (Array.isArray(this.FormData.demandImg)) {
						this.FormData.demandImg = this.FormData.demandImg.join(',')
					}

					if (this.openType === 'edit') {
						this.$u.api.updateRequirement(this.FormData).then(res => {
							uni.showToast({
								title: '更新成功',
								icon: 'success'
							})

							if (!this.userInfo.openId) {
								uni.showModal({
									title: '绑定微信公众号',
									content: '点击确定前往绑定微信公众号，可以收到实时消息通知',
									showCancel: true,
									success: ({
										confirm,
										cancel
									}) => {
										if (confirm) {
											uni.navigateBack()
											uni.navigateTo({
												url: '/pages/home/<USER>'
											})
										} else {
											uni.navigateBack()
										}
									}
								})
								return
							}

							setTimeout(() => {
								uni.navigateBack()
							}, 1500)
						}).catch(err => {
							uni.showToast({
								title: '更新失败',
								icon: 'none'
							})
						})
					} else {
						this.$u.api.addRequirement(this.FormData).then(res => {
							if (res.code === 200) {
								uni.showToast({
									title: '提交成功',
									icon: 'success'
								})
								setTimeout(() => {
									uni.navigateBack()
								}, 1500)
							} else {
								uni.showToast({
									title: res.msg,
									icon: 'none'
								})
							}
						}).catch(err => {
							uni.showToast({
								title: '提交失败',
								icon: 'none'
							})
						})
					}
				}).catch(err => {
					console.log('err', err);
				})
			},
			isFieldRequired(fieldName) {
				// 如果字段被隐藏，则不应该要求必填
				if (this.hiddenFields && this.hiddenFields[fieldName]) {
					return false;
				}

				if (this.customRules && this.customRules[fieldName] && this.customRules[fieldName].rules) {
					return this.customRules[fieldName].rules.some(rule => rule.required === true);
				}
				return false;
			},
			getNextMonthDate() {
				const date = new Date()
				date.setMonth(date.getMonth() + 1)
				const year = date.getFullYear()
				const month = (date.getMonth() + 1).toString().padStart(2, '0')
				const day = date.getDate().toString().padStart(2, '0')
				return `${year}-${month}-${day}`
			},
			// 添加加载城市选项的方法
			async loadCityOptions() {
				if (this.FormData.provinceCode) {
					const res = await this.$u.api.getRegion({
						code: this.FormData.provinceCode
					})
					this.cityOptions = res.data.map(item => ({
						text: item.name,
						value: item.code
					}))

					// 设置省份名称
					const province = this.provinceOptions.find(item => item.value === this.FormData.provinceCode)
					if (province) {
						this.FormData.provinceName = province.text
					}
				}
			},

			// 添加加载区域选项的方法
			async loadDistrictOptions() {
				if (this.FormData.cityCode) {
					const res = await this.$u.api.getRegion({
						code: this.FormData.cityCode
					})
					this.districtOptions = res.data.map(item => ({
						text: item.name,
						value: item.code
					}))

					// 设置城市名称
					const city = this.cityOptions.find(item => item.value === this.FormData.cityCode)
					if (city) {
						this.FormData.cityName = city.text
					}
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	view {
		box-sizing: border-box;
	}

	.container {
		padding: 24rpx;
	}

	.form-container {
		background-color: #fff;
		padding: 32rpx;
		border-radius: 8rpx;
	}

	::v-deep .uni-easyinput__content {
		border: none;
		border-bottom: 2rpx solid #F5F5F5;
		border-radius: 0;
	}

	::v-deep .uni-select {
		border: none;
		border-bottom: 2rpx solid #e5e5e5;
		border-radius: 0;
	}

	::v-deep .uni-select__input-box .uni-icons {
		font-size: 15px !important;
	}

	.tip {
		font-family: Source Han Sans;
		font-size: 22rpx;
		font-weight: 600;
		line-height: normal;
		letter-spacing: normal;
		color: #5077D4;
		position: relative;
		margin-bottom: 76rpx;
	}

	.tip::before {
		content: '';
		width: 100vw;
		height: 32rpx;
		background-color: #f5f5f5;
		position: absolute;
		left: -70rpx;
		top: 56rpx;
	}

	.tip-row {
		font-family: Source Han Sans;
		font-size: 22rpx;
		font-weight: 600;
		line-height: normal;
		letter-spacing: normal;
		color: #5077D4;
		position: relative;
		margin-bottom: 16rpx;
	}

	.tip-row:last-child {
		margin-bottom: 0;
	}

	.submit-box {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 32rpx 0;
	}

	.submit-text {
		width: 702rpx;
		height: 84rpx;

		color: #fff;
		background-color: #325AEE;
		border-radius: 8rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.uni-input {
		height: 100%;
		display: flex;
		align-items: center;
		border-bottom: 2rpx solid #e5e5e5;
		padding-left: 20rpx;
	}

	.form-avatar {
		width: 100%;
		height: 96rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 0 0 52rpx 0;

		image {
			width: 96rpx;
			height: 96rpx;
			border-radius: 50%;
		}
	}

	.location-picker {
		display: flex;
		justify-content: space-between;
		margin-bottom: 10px;
	}

	.location-picker>uni-data-select {
		width: 32%;
	}
</style>