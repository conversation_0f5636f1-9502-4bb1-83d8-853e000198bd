<template>
    <div class="message-container">
        <navigator class="tip-text" url="/pages/home/<USER>">
            <u-icon name="info-circle" color="#4075FF" size="18"></u-icon>
            <text>点击绑定公众号，即可接收实时消息</text>
        </navigator>
        <div class="message-item" v-for="(item, index) in messageList" :key="index">
            <div class="message-content">
                <div class="message-title">{{item.title || '系统通知'}}</div>
                <div class="message-subtitle">{{item.content || '系统更新通知'}}</div>
                <div class="message-time">{{item.createTime}}</div>
            </div>
            <div class="message-action">
                <button class="delete-btn" @click="deleteMessage(item,index)">删除</button>
            </div>
        </div>
        <view class="no-more" v-if="messageList.length > 0">
            <u-loadmore :status="status" dashed line :fontSize="12" />
        </view>
        <view style="padding-top:30%;" v-else>
            <u-empty mode="list" text="暂无数据">
            </u-empty>
        </view>
        <!-- 回到顶部 -->
        <view class="back-top-box">
            <u-back-top :scroll-top="scrollTop"></u-back-top>
        </view>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                messageList: [],
                query: {
                    current: 1,
                    size: 10
                },
                totalPages: 1,
                status: 'loadmore',
                scrollTop: 0
            }
        },
        onLoad() {
            this.loadMessageList();
        },
        onReachBottom() {
            this.status = 'loading';
            if (this.query.current >= this.totalPages) {
                this.status = 'nomore';
            } else {
                this.query.current++;
                setTimeout(() => {
                    this.loadMessageList();
                }, 200);
            }
        },
        onPageScroll(e) {
            this.scrollTop = e.scrollTop;
        },
        methods: {
            loadMessageList() {
                uni.showLoading({
                    title: '加载中'
                });
                this.$u.api.getNewsList(this.query).then(res => {
                    uni.hideLoading();
                    if (res.success) {
                        const newData = res.data.records || [];
                        this.totalPages = res.data.pages || 1;
                        
                        if (this.query.current === 1) {
                            this.messageList = newData;
                        } else {
                            this.messageList = [...this.messageList, ...newData];
                        }
                        
                        if (this.query.current >= this.totalPages) {
                            this.status = 'nomore';
                        } else {
                            this.status = 'loadmore';
                        }
                    } else {
                        uni.showToast({
                            title: res.message || '获取消息列表失败',
                            icon: 'none'
                        });
                    }
                }).catch(err => {
                    uni.hideLoading();
                    uni.showToast({
                        title: '获取消息列表失败',
                        icon: 'none'
                    });
                    console.error('获取消息列表失败', err);
                });
            },
            deleteMessage(item, index) {
                uni.showModal({
                    title: '提示',
                    content: '确定删除此消息吗？',
                    success: res => {
                        if (res.confirm) {
                            // 如果有消息ID且有删除API，可以添加真实的删除请求
                            this.$u.api.removeNews(item.id).then(res => {
                                if (res.success) {
                                    this.messageList.splice(index, 1);
                                    uni.showToast({
                                        title: '删除成功',
                                        icon: 'success'
                                    });
                                }
                            });
                            
                            this.loadMessageList();
                            uni.showToast({
                                title: '删除成功',
                                icon: 'success'
                            });
                        }
                    }
                });
            }
        }
    }
</script>

<style lang="scss" scoped>
.message-container {
    width: 100%;
    padding: 20rpx;
    box-sizing: border-box;
    .tip-text {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: #4075FF;
        text-align: center;
        padding: 12px 16px;
        margin-bottom: 20px;
        background-color: #EDF2FF;
        border-radius: 8px;
        border: 1px solid #D6E4FF;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

        u-icon {
            margin-right: 6px;
        }
    }
    .message-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #ffffff;
        border-radius: 8rpx;
        padding: 30rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        box-sizing: border-box;
        
        &:active {
            transform: scale(0.98);
        }
        
        .message-content {
            flex: 1;
            
            .message-title {
                font-size: 32rpx;
                font-weight: 500;
                margin-bottom: 10rpx;
                color: #333333;
            }
            
            .message-subtitle {
                font-size: 28rpx;
                color: #666666;
                margin-bottom: 10rpx;
                line-height: 40rpx;
            }
            
            .message-time {
                font-size: 24rpx;
                color: #999999;
            }
        }
        
        .message-action {
            .delete-btn {
                background: #4075ff;
                color: white;
                border: none;
                border-radius: 8rpx;
                font-size: 28rpx;
                box-shadow: 0 4rpx 8rpx rgba(10, 157, 255, 0.2);
                transition: all 0.3s;
                
                &:active {
                    background: #4075ff;
                    box-shadow: 0 2rpx 4rpx rgba(10, 157, 255, 0.1);
                }
            }
        }
    }
    
    .no-more {
        margin-top: 40rpx;
        padding: 20rpx 0;
    }
    
    .back-top-box {
        position: fixed;
        bottom: 200rpx;
        right: 80rpx;
        z-index: 9;
    }
}
</style>