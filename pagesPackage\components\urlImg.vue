<template>
	<view class="page">
		<view class="z-upload">
			<view class="choose_body" v-if="FatherImgList.length<=max">
				<view class="bg-img" :style="{width: size+'rpx',height: height ? height+'rpx':size+'rpx'}"
					v-for="(item,index) in FatherImgList" :key="index" @tap="ViewImage"
					:data-url="FatherImgList[index]">
					<image :src="FatherImgList[index]" mode="aspectFill"></image>
					<uni-icons class="bg-img1" type="clear" size="24" color="#e5e5e5" @tap.stop="DelImg(index)"></uni-icons>
				</view>
				<view v-if="FatherImgList.length<max" class="choose_img"
					:style="{width: size+'rpx',height: height ? height+'rpx':size+'rpx'}" @tap="ChooseImage">
					<image src="@/static/icon/icon-Upload.png" class="tubiao"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		devUrl
	} from '@/common/setting';

	export default {
		/* 
			event  @uploadImage  参数：图片路径  arr
		 */
		props: {
			// 图片大小
			size: {
				type: Number,
				default: 180,
			},
			// 图片高度
			height: {
				type: Number,
				default: 0,
			},
			// 最大上传数量
			max: {
				type: Number,
				default: 4,
			},
			imgList: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
				isFlag: false,
				payload: null,
				upUrl: '/hztech-resource/oss/endpoint/put-file-attach',
				FatherImgList: [],
			}
		},
		methods: {
			// 拍照或者选择图片
			ChooseImage() {
				uni.chooseImage({
					count: this.max - parseInt(this.FatherImgList.length), //默认9
					sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album', 'camera'], //从相册选择
					success: (res) => {
						let tempFilePaths = res.tempFilePaths;
						uni.showLoading({
							title: '加载中',
							mask: true
						});
						for (let i = 0; i < tempFilePaths.length; i++) {
							this.imgRoute(tempFilePaths[i])
						}
					}
				});
			},
			//预览图片
			ViewImage(e) {
				uni.previewImage({
					urls: this.FatherImgList,
					current: e.currentTarget.dataset.url
				});
			},
			cancel() {
				this.isFlag = false
			},
			//删除图片
			DelImg(index) {
				this.FatherImgList.splice(index, 1)
			},
			// 上传图片
			imgRoute(tempFilePaths) {
				uni.uploadFile({
					url: devUrl + this.upUrl,
					filePath: tempFilePaths, //这个就是我们上面拍照返回或者先中照片返回的数组
					name: 'file',
					header: {
						'Authorization': 'Basic aHp0ZWNoLWdjeWotYXBwOmh6dGVjaC1nY3ktc2VjcmV0',
						'Hztech-Auth': `bearer ` + uni.getStorageSync('accessToken'),
					},
					success: (uploadFileRes) => {
						let json = JSON.parse(uploadFileRes.data)
						if (json.code == 200) {
							setTimeout(function() {
								uni.hideLoading();
							}, 1000);
							this.FatherImgList.push(json.data.link)
							// this.$emit('getImage', this.imgList)
							console.log(this.FatherImgList.length, "this.FatherImgList")
						} else {
							uni.hideLoading();
							uni.showToast({
								title: '上传失败',
								icon: 'none',
								duration: 2000
							})
						}
					},
					fail: (err) => {
						uni.hideLoading();
						uni.showToast({
							title: '上传失败',
							icon: 'none',
							duration: 2000
						})
						// console.log('上传失败：', err)
					}
				});
			}
		},
		watch: {
			FatherImgList: {
				handler(val, old) {
					this.FatherImgList = val
					this.$emit('getImage', val)
				},
				immediate: true,
				deep: true
			},
			imgList: {
				handler(val) {
					if (val && val.length > 0) {
						this.FatherImgList = [...val]
					}
				},
				immediate: true,
				deep: true
			}
		}
	}
</script>

<style lang="scss" scoped>
	.z-upload {
		// max-height: 456.15rpx;
		// margin-top: 30rpx;
		box-sizing: border-box;
		// overflow: hidden;
		// overflow-y: auto;
	}

	.choose_img {
		width: 116rpx;
		height: 116rpx;
		border-radius: 10rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		// border: 1px dashed #CCCCCC;
		border-radius: 8rpx;

		.tubiao {
			width: 100%;
			height: 100%;
			// background-image:url('http://***********:9000/iot3/upload/20230823/0a6a63aea93ce3f7a259dcd62bfd3ce8.png');
			// background-repeat: no-repeat;
			// background-size: contain;
		}
	}

	.choose_title {
		font-size: 28rpx;
		font-weight: 700;
		padding: 30rpx;
	}

	.choose_body {
		width: 100%;
		height: 116rpx;

		display: flex;

		.bg-img {
			width: 116rpx;
			height: 116rpx;
			border-radius: 10rpx;
			overflow: hidden;
			position: relative;
			// margin-top: 20rpx;
			margin-right: 10rpx;
			// margin-bottom: 30rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.bg-img1 {
			position: absolute;
			right: 0;
			top: 0;
		}
	}
</style>