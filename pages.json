{"pages": [{"path": "pages/home/<USER>", "style": {"navigationBarTitleText": "首页", "enablePullDownRefresh": true, "onReachBottomDistance": 80, "navigationStyle": "custom"}}, {"path": "pages/home/<USER>", "style": {"navigationBarTitleText": "供需平台", "enablePullDownRefresh": true, "onReachBottomDistance": 80}}, {"path": "pages/user/center", "style": {"navigationBarTitleText": "我的", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/user/personalData", "style": {"navigationBarTitleText": "个人资料", "enablePullDownRefresh": false}}, {"path": "pages/webview/index", "style": {"navigationBarTitleText": "", "enableShareAppMessage": true}}, {"path": "pages/webview/specified", "style": {"navigationBarTitleText": ""}}, {"path": "pages/login/login-account", "style": {"navigationBarTitleText": "账号登录", "enablePullDownRefresh": false}}, {"path": "pages/home/<USER>", "style": {"navigationBarTitleText": "公众号授权"}}], "subPackages": [{"root": "pagesPackage/", "pages": [{"path": "tenderInfo/tenderInfo", "style": {"navigationBarTitleText": "招标中标信息", "enableShareAppMessage": true}}, {"path": "projectDetail/projectDetail", "style": {"navigationBarTitleText": "补充公告详情", "enableShareAppMessage": true}}, {"path": "projectSearch/projectSearch", "style": {"navigationBarTitleText": "工程项目品牌表查询", "enableShareAppMessage": true}}, {"path": "searchDetail/searchDetail", "style": {"navigationBarTitleText": "招标公告详情", "enableShareAppMessage": true}}, {"path": "search/search", "style": {"navigationBarTitleText": "供应商查询", "enableShareAppMessage": true}}, {"path": "candidate<PERSON><PERSON><PERSON>/candidate<PERSON><PERSON><PERSON>", "style": {"navigationBarTitleText": "中标候选人详情", "enableShareAppMessage": true}}, {"path": "resultDetail/resultDetail", "style": {"navigationBarTitleText": "中标结果详情", "enableShareAppMessage": true}}, {"path": "adverdetail/adverdetail", "usingComponents": {"mp-html": "mp-html"}, "style": {"navigationBarTitleText": "loading..."}}, {"path": "agreement/agreement", "style": {"navigationBarTitleText": "用户服务隐私政策协议"}}, {"path": "addressBook/addressBook", "style": {"navigationBarTitleText": "项目通讯录"}}, {"path": "addressBook/addressBookDetail", "style": {"navigationBarTitleText": "项目详情"}}, {"path": "requirementRelease/addRequirement", "style": {"navigationBarTitleText": "需求发布"}}, {"path": "requirementRelease/requirementDetail", "style": {"navigationBarTitleText": "需求详情"}}, {"path": "my/my<PERSON><PERSON>d", "style": {"navigationBarTitleText": "我的需求"}}, {"path": "my/demandHistory", "style": {"navigationBarTitleText": "需求浏览记录"}}, {"path": "my/membershipPackage", "style": {"navigationBarTitleText": "会员套餐"}}, {"path": "my/membershipPackageDetail", "style": {"navigationBarTitleText": "会员套餐详情"}}, {"path": "my/myMessage", "style": {"navigationBarTitleText": "我的消息"}}]}], "globalStyle": {"navigationBarTextStyle": "black", "navigationBarTitleText": "", "navigationBarBackgroundColor": "#fff", "backgroundColor": "#F7F7F7", "app-plus": {"background": "#efeff4"}}, "tabBar": {"color": "#A6ABB5", "selectedColor": "#4075FF", "borderStyle": "white", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/home/<USER>", "iconPath": "static/images/tabbar/home.png", "selectedIconPath": "static/images/tabbar/home_selected.png", "text": "首页"}, {"pagePath": "pages/home/<USER>", "iconPath": "static/images/tabbar/find.png", "selectedIconPath": "static/images/tabbar/find_selected.png", "text": "供需平台"}, {"pagePath": "pages/user/center", "iconPath": "static/images/tabbar/user.png", "selectedIconPath": "static/images/tabbar/user_selected.png", "text": "我的"}]}}