import http from '@/http/api.js'

// 获取token
const token = (tenantId, username, password, type) => {
  return http.request({
    url: '/hztech-auth/oauth/token',
    method: 'POST',
    header: {
      'Tenant-Id': tenantId
    },
    params: {
      tenantId,
      username,
      password,
      grant_type: "password",
      scope: "all",
      type
    }
  })
}
// 获取用户信息
const userInfo = (params) => {
  return http.request({
    url: '/hztech-eng/app/detail',
    method: 'GET',
    data: params
  })
}
// 解密用户信息
const decodeUserInfo = (params) => {
  return http.request({
    url: '/hztech-eng/app/decodeUserInfo',
    method: 'GET',
    data: params
  })
}
// 获取token
const getToken = (params) => {
  return http.request({
    url: '/hztech-auth/oauth/token',
    method: 'POST',
    data: params
  })
}
//修改用户信息
const updateUser = (params) => {
  return http.request({
    url: '/hztech-eng/app/update',
    method: 'POST',
    header: {
      'Content-Type': 'application/json' // 设置Content-Type
    },
    data: params
  })
}
const firstAuthUpdate = (params) => {
  return http.request({
    url: '/hztech-eng/app/firstAuthUpdate',
    method: 'POST',
    header: {
      'Content-Type': 'application/json' // 设置Content-Type
    },
    data: params
  })
}
//获取轮播图数据
const getBannerList = (params) => {
  return http.request({
    url: '/hztech-eng/app/EngAppAdvertisement/list',
    method: 'GET',
    data: params
  })
}
//获取轮播图数据
const getBannerSpeed = (params) => {
  return http.request({
    url: '/hztech-eng/app/EngAppAdvertisement/getSpeed',
    method: 'GET',
    data: params
  })
}
//获取文章列表
const engWxArticle = (params) => {
  return http.request({
    url: '/hztech-eng/app/engWxArticle/list',
    method: 'GET',
    data: params
  })
}
//退出登录
const logout = () => {
  return http.request({
    url: '/hztech-auth/oauth/appLogout',
    method: 'GET'
  })
}
//主页搜索
const homePageSearch = (params) => {
  return http.request({
    url: '/hztech-eng/app/homePageSearch',
    method: 'GET',
    data: params
  })
}
//招标公告分页
const purchasePage = (params) => {
  return http.request({
    url: '/hztech-eng/app/bid/purchasePage',
    method: 'GET',
    data: params
  })
}
//中标候选人列表
const candidatePage = (params) => {
  return http.request({
    url: '/hztech-eng/app/bid/candidatePage',
    method: 'GET',
    data: params
  })
}
//补充公告
const supplement = (params) => {
  return http.request({
    url: '/hztech-eng/app/bid/supplementPage',
    method: 'GET',
    data: params
  })
}
//中标结果公告
const biaoResult = (params) => {
  return http.request({
    url: '/hztech-eng/app/bid/resultPage',
    method: 'GET',
    data: params
  })
}
//附件列表
const fileList = (params) => {
  return http.request({
    url: '/hztech-eng/Accessory/list',
    method: 'GET',
    data: params
  })
}
//招标公告详情
const purchaseDetail = (params) => {
  return http.request({
    url: '/hztech-eng/app/bid/purchaseDetail',
    method: 'GET',
    data: params
  })
}
//补充公告详情
const supplementDetail = (params) => {
  return http.request({
    url: '/hztech-eng/app/bid/supplementDetail',
    method: 'GET',
    data: params
  })
}
//中标候选人公告详情
const candidateDetaill = (params) => {
  return http.request({
    url: '/hztech-eng/app/bid/candidateDetail',
    method: 'GET',
    data: params
  })
}
//中标结果公告详情
const resultDetail = (params) => {
  return http.request({
    url: '/hztech-eng/app/bid/resultDetail',
    method: 'GET',
    data: params
  })
}
//首页文章阅读量增加
const readCountAdd = (params) => {
  return http.request({
    url: '/hztech-eng/app/readCountAdd',
    method: 'GET',
    data: params
  })
}
//广告详情
const adverdetail = (params) => {
  return http.request({
    url: '/hztech-eng/app/EngAppAdvertisement/detail',
    method: 'GET',
    data: params
  })
}
//项目搜索
const esPage = (params) => {
  return http.request({
    url: '/hztech-eng/app/bid/esPage',
    method: 'GET',
    data: params
  })
}
//详情页 附件列表
const projectList = (params) => {
  return http.request({
    url: '/hztech-eng/Accessory/list',
    method: 'GET',
    data: params
  })
}
const getDictionary = (params) => {
  return http.request({
    url: '/hztech-system/dict/dictionary',
    method: 'GET',
    data: params
  })
}
const getDictionaryBiz = (params) => {
  return http.request({
    url: '/hztech-system/dict-biz/dictionary',
    method: 'GET',
    data: params
  })
}
//小程序首页菜单
const getListMenu = (params) => {
  return http.request({
    url: '/hztech-eng/app/listMenu',
    method: 'GET',
    data: params
  })
}
//获取手机号
const getUserPhone = (code) => {
  return http.request({
    url: `/hztech-eng/app/getUserPhone?code=${code}`,
    method: 'GET',
  })
}

export default {
  token,
  userInfo,
  decodeUserInfo,
  getToken,
  updateUser,
  firstAuthUpdate,
  getBannerList,
  getBannerSpeed,
  engWxArticle,
  logout,
  homePageSearch,
  purchasePage,
  candidatePage,
  supplement,
  biaoResult,
  fileList,
  purchaseDetail,
  candidateDetaill,
  resultDetail,
  readCountAdd,
  adverdetail,
  esPage,
  projectList,
  getDictionary,
  getDictionaryBiz,
  getListMenu,
  getUserPhone,
  supplementDetail,
}