/**
 * 全局变量配置
 */
module.exports = {
  // 应用名
  name: 'hztech-gcyj-app',
  // 应用logo，支持本地路径和网络路径
  logo: '/static/images/logo.png',
  // 版本号
  version: '1.0.0',
  // 开发环境接口Url   本地 http://192.168.3.61:8033   测试 https://gcyj.hzdssoft.com/api   正式 https://yijia.hzdssoft.com/api
  devUrl: 'http://gcyj.hzdssoft.com/api',
  // devUrl: 'http://192.168.3.121:8033',
  // 线上环境接口Url
  prodUrl: 'https://yijia.hzdssoft.com/api',
  // 后端数据的接收方式application/json;charset=UTF-8或者application/x-www-form-urlencoded;charset=UTF-8
  contentType: 'application/x-www-form-urlencoded;charset=UTF-8',
  // 后端返回状态码
  codeName: 'code',
  // 操作正常code
  successCode: 200,
  // 登录失效code
  invalidCode: 401,
  // 客户端ID
  clientId: 'hztech-gcyj-app',
  // 客户端密钥
  clientSecret: 'hztech-gcy-secret',
  //oss图片地址
  ossUrl: '/hztech-resource/oss/endpoint/put-file'
}