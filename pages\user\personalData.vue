<template>
	<view class="container">
		<form @submit="onSubmit">
			<view class="content">
				<view class="title">完善信息方便业务对接，并可申请加入微信资源群!</view>
				<view class="form">
					<view class="cell" style="justify-content: flex-start;">
						<view class="name">头像</view>
						<button class="avatar-btn" type="balanced" open-type="chooseAvatar"
							@chooseavatar="onChooseavatar">
							<image :src="userInfo.avatar || '/static/images/tabbar/user_selected.png'"
								class="avatar-image" />
						</button>
					</view>
					<view class="cell">
						<view class="name"><span style="color: red;">*</span>姓名</view>
						<input class="ipt" maxlength="4" v-model="userInfo.userName" @input="userNameInput"
							@blur="userNameInput" placeholder="请输入姓名" />
					</view>
					<view class="cell">
						<view class="name"><span style="color: red;">*</span>手机号</view>
						<input class="ipt" maxlength="11" disabled v-model="userInfo.userPhone" placeholder="请获取手机号" />
						<button class="btn" type="default" open-type="getPhoneNumber"
							@getphonenumber="getPhoneNumber">获取手机号</button>
					</view>
					<view class="cell">
						<view class="name"><span style="color: red;">*</span>公司名称</view>
						<input class="ipt" maxlength="20" v-model="userInfo.companyName" placeholder="请输入公司名称" />
					</view>
					<view class="cell">
						<view class="name"><span style="color: red;">*</span>单位属性</view>
						<view class="select">
							<uni-data-select v-model="userInfo.publisherType"
								:localdata="companyTypeList"></uni-data-select>
						</view>
					</view>
					<view class="cell">
						<view class="name"><span style="color: red;">*</span>岗位职务</view>
						<view class="select">
							<uni-data-select v-model="userInfo.companyPost" :localdata="jobTitleList"></uni-data-select>
						</view>
					</view>
					<!-- <view class="cell">
            <view class="name">微信号</view>
            <input class="ipt" maxlength="20" v-model="userInfo.wxNumber"/>
          </view> -->
					<view class="cell">
						<view class="name">常住城市</view>
						<input class="ipt" maxlength="10" v-model="userInfo.city" placeholder="请输入常住城市" />
					</view>
					<view class="cell">
						<view class="name">家乡</view>
						<input class="ipt" maxlength="10" v-model="userInfo.homeTown" placeholder="请输入家乡" />
					</view>
					<view class="cell">
						<view class="name">业务或专长</view>
						<input class="ipt" maxlength="10" v-model="userInfo.business" placeholder="请输入业务或专长" />
					</view>
					<view class="vip-show" v-if="reSubmit">
						<view class="vip-show-item">
							当前会员等级：{{userLevelList.find(item => item.dictKey == userInfo.userLevel).dictValue}}</view>
						<view class="vip-show-item">下次认证时间：{{userInfo.vipEndTime.split(' ')[0]}}</view>
					</view>
				</view>
			</view>
			<view class="submit-btn-box">
				<view  v-if="reSubmit" class="submit-btn" @click="submitCertification">重新提交认证</view>
				<view  v-if="!reSubmit" class="submit-btn" @click="updateInfo">保存</view>
				<view  v-if="!reSubmit" class="submit-btn" @click="loginOut">退出登录</view>
			</view>
		</form>
	</view>
</template>

<script>
	import {
		Base64
	} from '@/utils/base64.js';
	import {
		options
	} from '@/http/config.js';
	import {
		clientId,
		clientSecret
	} from '@/common/setting';

	export default {
		data() {
			return {
				// userInfo: {
				// 	avatar: ''
				// },
				detail: {},
				reSubmit: false,
				userLevelList: [],
				companyTypeList: [],
				jobTitleList: [],
				firstSubmit: false
			};
		},
		onLoad(e) {
			if (e.type == 'reSubmit') {
				this.reSubmit = true
				this.getCompanyType()
				this.$u.api.getDictionary({
					code: 'user_level'
				}).then(res => {
					this.userLevelList = res.data
				})
			}
			this.getUserInfo()
			this.getCompanyType()
		},
		methods: {
			getCompanyType() {
				this.$u.api.getDictionaryBiz({
					code: 'publisher_type'
				}).then(res => {
					this.companyTypeList = res.data.map(item => ({
						text: item.dictValue,
						value: item.dictKey
					}))
				})
				this.$u.api.getDictionaryBiz({
					code: 'job_title'
				}).then(res => {
					this.jobTitleList = res.data.map(item => ({
						text: item.dictValue,
						value: item.dictKey
					}))
				})
			},
			submitCertification() {
				this.$u.api.authUpdate({
					id: this.userInfo.id
				}).then(res => {
					if (res.success) {
						this.getUserInfo()
						uni.showToast({
							title: '认证成功',
							icon: 'success'
						})
						setTimeout(() => {
							uni.navigateBack({ delta: 1 })
						}, 1500)
					}
				}).catch(err => {
					uni.showToast({
						title: err.data.msg,
						icon: 'none'
					})
				})
			},
			getPhoneNumber(e) {
				if (e.detail.errMsg == "getPhoneNumber:ok") {
					this.$u.api.getUserPhone(e.detail.code).then(res => {
						if (res.code === 200) {
							this.userInfo.userPhone = res.data
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}).catch(err => {
						uni.showToast({
							title: err.msg,
							icon: 'none'
						})
					})
				} else {
					console.log('用户拒绝授权');
				}
			},
			onSubmit(event) {
				event.preventDefault(); // 阻止页面刷新
			},
			onChooseavatar(e) {
				let that = this;
				let {
					avatarUrl
				} = e.detail;
				uni.showLoading({
					title: '加载中'
				});
				uni.uploadFile({
					url: options.baseURL + options.ossUrl, // 仅为示例，非真实的接口地址
					filePath: avatarUrl,
					name: 'file',
					header: {
						'Authorization': 'Basic ' + Base64.encode(clientId + ':' + clientSecret),
						'HzTech-Auth': 'bearer ' + uni.getStorageSync('accessToken') // 携带token
					},
					formData: {
						'user': 'test'
					},
					success: (uploadFileRes) => {
						let data = JSON.parse(uploadFileRes.data)
						that.userInfo.avatar = data.data.link
						uni.hideLoading()
					}
				})
			},
			userNameInput(e) {
				this.userInfo.userName = e.detail.value
			},
			//保存用户信息
			updateInfo() {
				let params = {
					id: this.userInfo.id,
					avatar: this.userInfo.avatar,
					// userName: this.userInfo.userName,
					userName: this.userInfo.userName,
					companyName: this.userInfo.companyName,
					email: this.userInfo.email,
					// wxNumber: this.userInfo.wxNumber,
					city: this.userInfo.city,
					homeTown: this.userInfo.homeTown,
					business: this.userInfo.business,
					companyPost: this.userInfo.companyPost,
					publisherType: this.userInfo.publisherType,
					userPhone: this.userInfo.userPhone,
				}
				if (params.userName == '') {
					uni.showToast({
						duration: 1000,
						title: '姓名不能为空',
						icon: 'none'
					});
					return;
				}
				if (params.companyName == '') {
					uni.showToast({
						duration: 1000,
						title: '单位/公司不能为空',
						icon: 'none'
					});
					return;
				}
				if (params.userPhone == '') {
					uni.showToast({
						duration: 1000,
						title: '手机号不能为空',
						icon: 'none'
					});
					return;
				}
				if (params.publisherType == '') {
					uni.showToast({
						duration: 1000,
						title: '单位属性不能为空',
						icon: 'none'
					});
					return;
				}
				if (params.companyPost == '') {
					uni.showToast({
						duration: 1000,
						title: '岗位职务不能为空',
						icon: 'none'
					});
					return;
				}
				if (params.userPhone && !/^1\d{10}$/.test(params.userPhone)) {
					return uni.showToast({
						title: '请输入正确的手机号',
						duration: 2000,
						icon: 'none'
					})
				}
				if (this.firstSubmit) {

					this.$u.api.firstAuthUpdate(params).then(data => {
						if (data.success) {
							uni.showToast({
								duration: 1000,
								title: '保存成功',
								icon: 'success'
							})
							setTimeout(function() {
								uni.switchTab({
									url: '/pages/home/<USER>'
								})
							}, 1000)
						}
					})
				} else {
					this.$u.api.updateUser(params).then(data => {
						if (data.success) {
							uni.showToast({
								duration: 1000,
								title: '保存成功',
								icon: 'success'
							})
							setTimeout(function() {
								uni.switchTab({
									url: '/pages/home/<USER>'
								})
							}, 1000)
						}
					})
				}
			},
			getUserInfo() {
				let userInfo = uni.getStorageSync('userInfo')
				let params = {
					id: userInfo.user_id
				}
				this.$u.api.userInfo(params).then(data => {
					if (data.code === 200) {
						const row = Object.assign({}, this.userInfo, data.data)
         				this.$u.vuex('userInfo', row)
						if (!this.userInfo.companyName || !this.userInfo.userName || !this.userInfo.userPhone || !this.userInfo.publisherType || !this.userInfo.companyPost) {
							uni.showToast({
								title: '完善个人信息体验更多功能！',
								duration: 2000,
								icon: 'none'
							})
							this.firstSubmit = true
						}
					}
					if (data.data.code === 401 || data.data.code === 400) {
						uni.navigateTo({
							url: '/pages/login/login-account'
						})
						uni.showToast({
							title: '登录已过期，请重新登录！',
							duration: 2000,
							icon: 'none'
						})
					}
				}).catch(err => {
					// this.$u.func.showToast({
					//   title: err,
					// })
					if (err.data.code === 4001 || err.data.code === 401 || err.data.code === 400) {
						uni.showToast({
							title: '登录已过期，请重新登录！',
							duration: 3000,
							icon: 'none'
						})
						uni.navigateTo({
							url: '/pages/login/login-account'
						})
					} else {
						uni.showToast({
							title: err.data.msg,
							icon: 'none'
						});
						this.$u.func.showToast({
							title: err,
						})
					}
				})
			},
			changePicker(name, e) {
				this.detail[name] = this[`${name}Picker`][e.detail.value].name;
			},
			changeDefaultPicker(name, e) {
				this.detail[name] = e.detail.value;
			},
			handleChooseImg() {
				let that = this;
				uni.chooseImage({
					count: 1,
					success(e) {
						// 成功选择图片后
						const filePath = e.tempFilePaths[0]; // 获取文件路径
						uni.uploadFile({
							url: options.baseURL + options.ossUrl, //仅为示例，非真实的接口地址
							filePath: filePath,
							name: 'file',
							header: {
								'Authorization': 'Basic ' + Base64.encode(clientId + ':' + clientSecret),
								'HzTech-Auth': 'bearer ' + uni.getStorageSync('accessToken') // 携带token
							},
							formData: {
								'user': 'test'
							},
							success: (uploadFileRes) => {
								let url = JSON.parse(uploadFileRes.data)
								that.userInfo.avatar = url.data.link
							}
						})
					}
				})
			},
			// 退出登录
			loginOut() {
				let that = this
				uni.showModal({
					title: '退出登录',
					content: '确认退出登录吗?',
					success: (res) => {
						if (res.confirm) {
							uni.showLoading({})
							that.$u.api.logout().then(data => {
								if (data.code == 200) {
									uni.hideLoading()
									uni.setStorageSync('accessToken', '')
									uni.setStorageSync('userInfo', '')
									this.$u.vuex('isLogin', false)
									this.$u.vuex('accessToken', '')
									this.$u.vuex('userInfo', '')
									setTimeout(function() {
										if (!uni.getStorageSync('accessToken')) {
											uni.switchTab({
												url: '/pages/home/<USER>'
											})
											return false
										}
									}, 500)
								}
							}).catch(err => {
								uni.hideLoading()
								this.$u.func.showToast({
									title: err,
								})
							})
						} else if (res.cancel) {
							// 用户点击了取消按钮
							console.log('用户取消了授权');
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	view {
		box-sizing: border-box;
	}

	.container {
		background-color: #f7f7f7;
		min-height: 100vh;
		overflow: hidden;
	}

	.complie {
		vertical-align: middle;
		padding: 10rpx 20rpx;
		font-size: 32rpx;
		font-family: Source Han Sans CN;
		font-weight: 400;
		color: #14b9c8;
	}

	.content {
		border-radius: 8rpx;
		background: #FFFFFF;
		margin: 24rpx;
	}

	.avatar-btn {
		margin: 0;
		padding: 0;
		line-height: 0;
	}

	.avatar-image {
		width: 66rpx;
		height: 66rpx;
		border-radius: 50%;
	}

	::v-deep .uni-select__input-box {
		font-size: 24rpx;
		font-family: Source Han Sans CN;
		font-weight: bold;
	}

	::v-deep .uni-select {
		border: none;
		border-bottom: 2rpx solid #F5F5F5;
		border-radius: 0;
		padding: 24rpx 0;
		height: auto;
	}

	::v-deep .uni-select__input-box .uni-icons {
		font-size: 15px !important;
	}

	.form {
		padding: 0 32rpx 24rpx 32rpx;

		.cell {
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 30rpx;
			font-family: Source Han Sans CN;
			font-weight: bold;
			color: #666666;

			.name {
				font-family: Source Han Sans;
				font-size: 28rpx;
				font-weight: normal;
				line-height: normal;
				letter-spacing: normal;
				color: #333333;
				width: 164rpx;
				padding: 36rpx 0;
			}

			.btn {
				height: 64rpx;
				border-radius: 200rpx;
				background: #5387F3;
				font-family: Source Han Sans;
				font-size: 26rpx;
				font-weight: 500;
				line-height: normal;
				letter-spacing: normal;
				color: #FFFFFF;
				display: flex;
				justify-content: center;
				align-items: center;
			}

			.ipt {
				flex: 1;
				font-size: 24rpx;
				font-family: Source Han Sans CN;
				font-weight: bold;
				color: #333333;
				border-bottom: 2rpx solid #F5F5F5;
				padding: 36rpx 0;
			}

			.hold {
				font-size: 27rpx;
				font-family: Source Han Sans CN;
				font-weight: bold;
				color: #c8c8ce;
			}

			.select {
				flex: 1;
			}
		}
	}

	.vip-show {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-family: Source Han Sans;
		font-size: 22rpx;
		font-weight: 600;
		line-height: normal;
		letter-spacing: normal;
		color: #5077D4;
		margin-top: 24rpx;

		.vip-show-item {
			white-space: nowrap;
		}
	}

	.submit-btn-box {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-wrap: wrap;
	}

	.submit-btn {
		width: 702rpx;
		height: 84rpx;
		border-radius: 8rpx;
		background: #325AEE;
		font-family: Source Han Sans;
		font-size: 32rpx;
		font-weight: 500;
		line-height: 186.45%;
		letter-spacing: 0.14em;
		color: #FFFFFF;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.back {
		margin-top: 40rpx;
		text-align: center;
		height: 109rpx;
		background: #ffffff;
		font-size: 34rpx;
		font-family: Source Han Sans CN;
		font-weight: bold;
		color: #141414;
		line-height: 109rpx;
	}

	button::after {
		border: none;
	}

	button {
		background: none;
		color: skyblue;
		font-size: 28rpx;
	}

	.title {
		color: red;
		text-align: center;
		font-size: 24rpx;
		letter-spacing: 2px;
		padding: 20rpx;
		background: #fff;
		border-bottom: 1px dashed #efefef;
	}
</style>