<template>
  <view class="check-box">
    <checkbox-group @change="radioChange">
      <label v-for="item in radioGroup" :key="item.value" style="float: left;">
        <checkbox :value="item.value" :checked="!item.checked"/>
        {{ item.name }}
      </label>
      <view style="color:#ff001e;float: left" @click="agreement">
        《用户服务隐私政策协议》
      </view>
    </checkbox-group>
  </view>
</template>

<script>
export default {
  data() {
    return {
      activeRadio: false, // 存的是选中的value值
      radioGroup: [{
        name: '勾选代表您已同意',
        value: '0',
        checked: 'false'
      }],
    }
  },
  methods: {
    agreement() {
      this.$emit("toagreement")
    },
    radioChange(e) {
      if (e.detail.value == '0') {
        this.activeRadio = true; //同意
        this.$emit("checkval", true)
      } else {
        this.activeRadio = false;
        this.$emit("checkval", false)
      }
    }
  }
}
</script>

<style>
checkbox {
  transform: scale(0.7);
}

.check-box {
  color: #888;
  font-size: 28rpx;
  float: left;
  margin: 20rpx 40rpx;
}
</style>
