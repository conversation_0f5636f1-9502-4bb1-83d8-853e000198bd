<template>
  <web-view :src="webUrl"></web-view>
</template>

<script>
export default {
  data() {
    return {
      id: '',
      webUrl: '',
    }
  },
  onLoad(options) {
    if (options.url) {
      this.webUrl = decodeURIComponent(options.url)
    }
    if (options.id) {
      this.id = options.id
      this.readCountAdd()
    }
  },
  onUnload() {
    uni.$emit('refreshList', {msg: '页面更新'})
  },
  onShareAppMessage() {
    return {
      title: '',
      path: '/pages/webview/index?url=' + this.webUrl,
    }
  },
  methods: {
    readCountAdd() {
      let params = {id: this.id}
      this.$u.api.readCountAdd(params).then(data => {
        if (data.success) {
          console.log(data.data);
        }
      }).catch(err => {
        this.$u.func.showToast({
          title: err,
        })
      })
    }
  }
}
</script>