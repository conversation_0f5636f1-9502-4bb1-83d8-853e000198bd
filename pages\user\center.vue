<template>
  <view class="container">
    <view class="head">
      <top-nav :is-fixed="false" :border-bottom="false" :is-back="false" title="个人中心"
        :background="{ background: 'transprent' }" title-color="#FFFFFF">
      </top-nav>
      <view class="head-bg"></view>
      <!-- 用户信息 -->
      <view class="user-box">
        <view class="left">
          <image v-if="userInfo.avatar" :src="userInfo.avatar" class="avatar"></image>
          <image v-else src="/static/images/tabbar/user_selected.png" class="avatar"></image>
          <view class="user-name" v-if="isLogin">
            <view class="user-name-box">
              <view style="font-size: 32rpx">{{ userInfo.userName }}</view>
              <view class="ft-12" v-if="userInfo.userLevel === 1">普通用户</view>
              <view class="ft-12" v-if="userInfo.userLevel === 2">
                <image src="/static/icon/vip.png" class="set-icon" mode="widthFix"></image>
                体验会员
              </view>
              <view class="ft-12" v-if="userInfo.userLevel === 3">
                <image src="/static/icon/vip.png" class="set-icon" mode="widthFix"></image>
                付费会员
              </view>
            </view>
            <view class="left-time" v-if="userInfo.vipEndTime">会员至{{ userInfo.vipEndTime.split(' ')[0] }}</view>
          </view>
          <view class="user-name" v-if="!isLogin">点击登录</view>
        </view>
        <view v-if="isLogin" @click.stop="$u.func.route('/pages/user/personalData')" class="edit-btn">编辑资料
        </view>
      </view>
      <view class="jump-box">
        <navigator class="jump-item" url="/pages/user/personalData?type=reSubmit" hover-class="navigator-hover">
          <image src="/static/icon/auth.png" class="jump-icon"></image>
          <view>会员认证</view>
        </navigator>
        <navigator class="jump-item" url="/pagesPackage/my/demandHistory" hover-class="navigator-hover">
          <image src="/static/icon/record.png" style="width: 42rpx;" class="jump-icon"></image>
          <view>浏览记录</view>
        </navigator>
        <navigator class="jump-item" url="/pagesPackage/my/myMessage" hover-class="navigator-hover">
          <image src="/static/icon/notice.png" style="width: 44rpx;" class="jump-icon"></image>
          <view>通知消息</view>
        </navigator>
      </view>
    </view>

    <view class="vip-box">
      <view class="vip-box-left">
        <image src="/static/icon/vip2.png" class="vip-icon2"></image>
        查看会员更多权益
      </view>
      <navigator class="vip-box-right" url="/pagesPackage/my/membershipPackage" hover-class="navigator-hover">
        查看会员
      </navigator>
    </view>

    <view class="demand-box">
      <view class="demand-box-header">
        <view>我的需求</view>
        <view class="demand-box-header-right" @click="$u.func.route('/pagesPackage/my/myDemand')">查看全部</view>
      </view>
      <view class="demand-box-content" v-if="demandList.length > 0">
        <view class="demand-box-item" v-for="item in demandList" :key="item.id"
          @click="$u.func.route('/pagesPackage/my/myDemand')">
          <view class="demand-box-item-title">{{ item.demandTitle }}</view>
          <view class="demand-box-item-time">{{ item.createTime.split(' ')[0] }}</view>
        </view>
      </view>
      <view class="demand-box-content2" v-else>
        <view class="demand-box-content2-title">暂无数据</view>
      </view>


      <view v-if="isLogin" class="cell-box" @click="loginOut">退出登录</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // userInfo: {},
      h5: 'https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/05935841-1674-4bd8-85c9-9d140acae542.pdf',
      // isLogin: ''
      demandList: []
    };
  },
  onShow() {
    this.getUserInfo()
    this.getDemandList()
    // this.isLogin = uni.getStorageSync('isLogin')
  },
  methods: {
    getDemandList() {
      this.$u.api.getMyDemand({
        current: 1,
        size: 10
      }).then(data => {
        this.demandList = data.data.records.slice(0, 2)
      })
    },
    getUserInfo() {
      let userInfo = uni.getStorageSync('userInfo')
      let params = {
        id: userInfo.user_id
      }
      this.$u.api.userInfo(params).then(data => {
        if (data.success) {
          // this.userInfo = data.data;
          const row = Object.assign({}, this.userInfo, data.data)
          this.$u.vuex('userInfo', row)
        }
      }).catch(err => {
        if (err.data.code == 401 || err.data.code == 4001) {
          this.reset()
          setTimeout(function () {
            uni.redirectTo({
              url: '/pages/login/login-account'
            })
          }, 100)
        }
        this.$u.func.showToast({
          title: err,
        })
      })
    },
    reset() {
      uni.setStorageSync('accessToken', '')
      uni.setStorageSync('userInfo', '')
      this.userInfo = {}
      this.$u.vuex('isLogin', false)
      this.$u.vuex('accessToken', '')
      this.$u.vuex('userInfo', '')
    },
    // 退出登录
    loginOut() {
      let that = this
      uni.showModal({
        title: '退出登录',
        content: '确认退出登录吗?',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({})
            that.$u.api.logout().then(data => {
              if (data.code == 200) {
                uni.hideLoading()
                this.reset()
                setTimeout(function () {
                  if (!uni.getStorageSync('accessToken')) {
                    uni.switchTab({
                      url: '/pages/home/<USER>'
                    })
                    return false
                  }
                }, 500)
              }

            }).catch(err => {
              uni.hideLoading()
              this.$u.func.showToast({
                title: err,
              })
            })
          } else if (res.cancel) {
            console.log('用户取消了授权');
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  overflow: hidden;
}

.head {
  position: relative;
  top: 0;
  left: 0;
  z-index: 1;
  background: linear-gradient(180deg, #2249EA 0%, #68A3F7 100%);
  height: 456rpx;
  margin-bottom: 114rpx;

  .head-bg {
    position: absolute;
    left: 0;
    top: 0;
    z-index: -1;
    width: 100%;
    height: 456rpx;
  }
}

.jump-box {
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-radius: 8rpx;
  background: #FFFFFF;
  width: 702rpx;
  height: 180rpx;
  position: absolute;
  bottom: -90rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 111;

  .jump-item {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    font-family: Source Han Sans;
    font-size: 24rpx;
    font-weight: 600;
    line-height: 146.75%;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.12em;
    color: #333333;
  }

  .jump-icon {
    width: 52rpx;
    height: 52rpx;
    margin-bottom: 22rpx;
  }
}

.vip-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 24rpx 32rpx 24rpx;
  width: 702rpx;
  height: 138rpx;
  border-radius: 16rpx;
  background: #FFFFFF;
  padding: 43rpx 30rpx;
  box-sizing: border-box;

  .vip-box-left {
    display: flex;
    align-items: center;

    font-family: Source Han Sans;
    font-size: 28rpx;
    font-weight: 600;
    line-height: 146.75%;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.12em;
    color: #4075FF;

    .vip-icon2 {
      width: 40rpx;
      height: 40rpx;
      margin-right: 14rpx;
    }
  }

  .vip-box-right {
    height: 36rpx;
    border-radius: 10rpx;
    background: #4075FF;
    display: flex;
    align-items: center;
    padding: 8rpx 16rpx;

    font-family: Source Han Sans;
    font-size: 24rpx;
    font-weight: 500;
    line-height: 146.75%;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.12em;
    color: #FFFFFF;
  }
}

.demand-box {
  width: 702rpx;
  height: 564rpx;
  border-radius: 16rpx;
  background: #FFFFFF;
  margin: 0 24rpx;
  padding: 24rpx;
  box-sizing: border-box;

  .demand-box-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: Source Han Sans;
    font-size: 32rpx;
    font-weight: 500;
    line-height: 166.15%;
    letter-spacing: 0.14em;
    color: #333333;

    .demand-box-header-right {
      font-family: Source Han Sans;
      font-size: 26rpx;
      font-weight: 600;
      line-height: 166.15%;
      letter-spacing: 0.14em;
      color: #4075FF;
    }
  }

  .demand-box-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .demand-box-item {
      width: 654rpx;
      height: 200rpx;
      border-radius: 12rpx;
      background: #F5F6FA;
      box-sizing: border-box;
      padding: 24rpx 24rpx 20rpx 24rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-top: 24rpx;

      .demand-box-item-title {
        font-family: Source Han Sans;
        font-size: 26rpx;
        font-weight: normal;
        line-height: 1.5;
        letter-spacing: 0.14em;
        color: #333333;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        max-height: 78rpx;
        width: 100%;
      }

      .demand-box-item-time {
        display: flex;
        justify-content: flex-end;
        font-family: Source Han Sans;
        font-size: 26rpx;
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        color: #333333;
      }
    }
  }

  .demand-box-content2 {
    width: 100%;
    height: 462rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .demand-box-content2-title {
      font-family: Source Han Sans;
      font-size: 32rpx;
      font-weight: 500;
      line-height: 166.15%;
      letter-spacing: 0.14em;
      color: #333333;
    }
  }
}

.user-box {
  display: flex;
  justify-content: space-between;
  padding: 0 24rpx;
  margin-top: 36rpx;

  .left {
    display: flex;
    align-items: center;

    .avatar {
      width: 104rpx;
      height: 104rpx;
      background: #ffffff;
      border-radius: 50%;
    }

    .user-name {
      font-size: 36rpx;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: #ffffff;
      margin-left: 8px;

      .user-name-box {
        display: flex;
        align-items: center;
      }

      .left-time {
        margin-top: 6rpx;
        font-family: Source Han Sans;
        font-size: 22rpx;
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        color: #FFFFFF;
      }
    }

    .tag {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 5rpx 16rpx;
      border: 1px solid #f5f5f5;
      border-radius: 7rpx;
      font-size: 19rpx;
      font-family: Source Han Sans CN;
      font-weight: 300;
      color: #ffffff;
    }
  }

  .edit-btn {
    margin-top: 20rpx;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 164rpx;
    height: 58rpx;
    border-radius: 416rpx;
    background: #FFFFFF;
    font-family: Source Han Sans;
    font-size: 26rpx;
    font-weight: 500;
    line-height: normal;
    letter-spacing: normal;
    color: #3966EE;
  }
}

.nav {
  display: flex;
  margin: 36rpx 38rpx 0;
  padding: 36rpx 0 42rpx;

  .nav-item {
    width: calc(100% / 4);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    font-size: 25rpx;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;

    &:not(:last-of-type) {
      position: relative;

      &::after {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        content: '';
        display: block;
        width: 2rpx;
        background-color: #3ac4d1;
        height: 30rpx;
      }
    }

    .icon {
      width: 48rpx;
      height: 48rpx;
      margin-bottom: 6rpx;
    }
  }
}

.cell-box {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  bottom: 38rpx;
  width: 688rpx;
  height: 66rpx;
  border-radius: 624rpx;
  background: #4075FF;

  display: flex;
  align-items: center;
  justify-content: center;
  font-family: Source Han Sans;
  font-size: 26rpx;
  font-weight: 500;
  line-height: normal;
  text-align: center;
  letter-spacing: normal;
  color: #FFFFFF;
}

.ft-12 {
  box-sizing: border-box;
  padding: 8rpx 14rpx 8rpx 10rpx;
  height: 44rpx;
  border-radius: 56rpx;
  background: #FFFFFF;
  margin-left: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  font-family: Source Han Sans;
  font-size: 20rpx;
  font-weight: 500;
  line-height: normal;
  letter-spacing: normal;
  color: #5387F3;
}

.set-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}
</style>