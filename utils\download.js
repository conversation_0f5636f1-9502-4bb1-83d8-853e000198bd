function isImage(type) { //打开文件类型归类
  const imgtype = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp'];
  const filetype = ['doc', 'docx', 'xls', 'xlsx', 'pdf'];

  if (imgtype.includes(type.toLowerCase())) {
    return 0
  }
  if (filetype.includes(type.toLowerCase())) {
    return 1
  }
}

//预览功能
export const download = (url, type) => {
  if (isImage(type) === 0) {
    uni.previewImage({
      urls: [url]
    })
  } else if (isImage(type) === 1) {
    uni.showLoading({
      title: '加载中...'
    });
    //下载文件
    uni.downloadFile({ //只能是GET请求
      url: url, //请求地址(后台返回的码流地址)
      success: (res) => {
        //下载成功
        if (res.statusCode === 200) {
          //保存文件
          let tempFile = res.tempFilePath;
          //保存成功之后 打开文件
          uni.openDocument({
            filePath: tempFile,
            fileType: type,
            showMenu: false, //是否可以分享
            success: (res) => {
              uni.hideLoading()
              console.log(res);
            },
            fail: (e) => {
              uni.showToast({
                title: '文件已损坏,预览失败！',
                icon: "none"
              })
            }
          })
        }
      },
      fail: () => {
        uni.showToast({
          title: '文件已损坏,预览失败',
          icon: "none",
        })
      }
    });
  } else {
    uni.showToast({
      title: '不支持打开此文件类型',
      icon: 'none'
    })
  }
}