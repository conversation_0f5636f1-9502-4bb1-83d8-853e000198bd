<template>
    <div class="membership-container">
        <!-- 顶部提示文本 -->
        <div class="tip-text">
            <u-icon name="info-circle" color="#4075FF" size="18"></u-icon>
            <text>如需开通会员，请联系工程壹家管理员</text>
        </div>

        <!-- 会员套餐卡片列表 -->
        <div class="package-list">
            <!-- 会员套餐卡片1 -->
            <div class="package-card" v-for="item in packages" :key="item.id">
                <div class="package-info">
                    <div class="package-title">{{ item.packageName }}</div>
                </div>
                <div class="package-action">
                    <button class="detail-btn" @click="viewDetail(item)">详情</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            packages: []
        }
    },
    onLoad() {
        this.init()
    },
    methods: {
        init() {
            this.$u.api.getPackageList(1, -1).then(data => {
                this.packages = data.data.records
            })
        },
        viewDetail(item) {
            // 查看会员套餐详情
            uni.navigateTo({
                url: '/pagesPackage/my/membershipPackageDetail?id=' + item.id
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.membership-container {
    padding: 16px;
    background-color: #f5f5f5;
    min-height: 100vh;

    .tip-text {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: #4075FF;
        text-align: center;
        padding: 12px 16px;
        margin-bottom: 20px;
        background-color: #EDF2FF;
        border-radius: 8px;
        border: 1px solid #D6E4FF;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

        u-icon {
            margin-right: 6px;
        }
    }

    .package-list {
        .package-card {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #fff;
            padding: 20px 16px;
            margin-bottom: 20px;
            border-radius: 8px;

            .package-info {
                .package-title {
                    font-size: 16px;
                    color: #333;
                    font-weight: 500;
                }
            }

            .package-action {
                display: flex;
                justify-content: flex-end;

                .detail-btn {
                    width: 162rpx;
                    height: 64rpx;
                    border-radius: 8rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-family: Source Han Sans;
                    font-size: 26rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    margin-left: 40rpx;
                    background: #4075FF;
                    color: #fff;
                }
            }
        }
    }
}
</style>