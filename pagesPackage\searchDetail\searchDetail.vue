<template>
  <view class="wrap">
    <view class="tipinfo">以下信息仅供参考，请勿转载用于其它商业用途!</view>
    <view class="warnTitle">如若没有显示的信息请打开网址查看</view>
    <view class="list">
      <u-row customStyle="margin-bottom: 10px;">
        <u-col span="12">
          <span class="left-title">项目名称：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px;">
        <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
          <span @click="copyText(result.name)">{{ result.name }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px;">
        <u-col span="12">
          <span class="left-title">项目编号：</span>
        </u-col>
      </u-row>
      <u-row>
        <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
          <span @click="copyText(result.code)">{{ result.code }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px;">
        <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
          <span>{{ result.bidStatus }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px;">
        <u-col span="12">
          <span class="left-title">项目所在地：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px;">
        <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
          <span @click="copyText(result.region)">{{ result.region }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px;">
        <u-col span="12">
          <span class="left-title">采购方式：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px;">
        <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
          <span @click="copyText(result.type)">{{ result.type }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px;">
        <u-col span="12">
          <span class="left-title">发布时间：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px;">
        <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
          <span @click="copyText(result.beginTime)">{{ result.beginTime ? result.beginTime.substring(0, 10) : '' }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px;">
        <u-col span="12">
          <span class="left-title">网址链接：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px;">
        <u-col span="12"
               customStyle="text-align: left;font-size: 14px;color: #666;word-wrap: break-word;word-break: normal;">
          <span>{{ result.url }}</span>
        </u-col>
      </u-row>
      <u-row>
        <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
          <u-button text="复制链接" @click="copyUrl"></u-button>
        </u-col>
      </u-row>
    </view>

    <view class="list" style="margin: 20px 0;">
      <u-row customStyle="margin-bottom: 10px;">
        <u-col span="12">
          <span class="left-title">招标人：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px;">
        <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
          <span @click="copyText(result.bidPop)">{{ result.bidPop }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px;">
        <u-col span="12">
          <span class="left-title">招标人名称：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px;">
        <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
          <span @click="copyText(result.bidPopName)">{{ result.bidPopName }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px;">
        <u-col span="12">
          <span class="left-title">招标人联系方式：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px;">
        <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
          <span @click="copyText(result.bidPopPhone)">{{ result.bidPopPhone }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px;">
        <u-col span="12">
          <span class="left-title">代理人：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px;">
        <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
          <span @click="copyText(result.agencyPop)">{{ result.agencyPop }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px;">
        <u-col span="12">
          <span class="left-title">代理人名称：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px;">
        <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
          <span @click="copyText(result.agencyPopName)">{{ result.agencyPopName }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px;">
        <u-col span="12">
          <span class="left-title">代理人联系方式：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px;">
        <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
          <span @click="copyText(result.agencyPopPhone)">{{ result.agencyPopPhone }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 10px;">
        <u-col span="12">
          <span class="left-title">金额：</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px;">
        <u-col span="12" customStyle="text-align: left;font-size: 14px;color: #666;">
          <span>{{ result.sum }}</span>
        </u-col>
      </u-row>

      <view class="filebox" v-for="(item,index) in fileResult" :key="index" :class="result.length>1?'preLit':''">
        <u-row customStyle="margin-bottom: 20rpx">
          <u-col span="12">
            <span>附件名称：</span>
          </u-col>
        </u-row>
        <u-row>
          <u-col span="12" customStyle="text-align: left;">
            <span style="color: #555;" @click="copyText(item.name)">{{ item.name }}</span>
          </u-col>
        </u-row>
        <u-row customStyle="margin: 40rpx 0;">
          <u-col span="12">
            <u-button type="primary" :plain="true" text="预 览" @click="download(item.url, item.attachmentType)"></u-button>
          </u-col>
        </u-row>
      </view>
    </view>

    <view class="list">
      <dataTable :detailId="id" :content="content"></dataTable>

      <mp-html lazy-load scroll-table selectable use-anchor :content="result.purchaseWay"></mp-html>
    </view>
  </view>
</template>

<script>
import {download} from "@/utils/download";
import dataTable from "./dataTable.vue";

export default {
  components: {
    dataTable
  },
  data() {
    return {
      id: '',
      name: '',
      result: '',
      content: '',
      fileResult: [],
    }
  },
  onLoad(options) {
    if (!uni.getStorageSync('accessToken')) {
      uni.navigateTo({
        url: '/pages/login/login-account'
      })
    } else {
      this.id = options.id
      this.name = options.name
      this.content = options.content
      this.getDetail()
      this.fileDetail()
    }
  },
  onShareAppMessage() {
    return {
      title: this.name,
      path: `/pagesPackage/searchDetail/searchDetail?id=${this.id}&name=${this.name}&content=${this.content ? this.content : ''}`,
    }
  },
  onShareTimeline() {
    return {
      title: this.name,
      query: `id=${this.id}&name=${this.name}&content=${this.content ? this.content : ''}`,
    }
  },
  methods: {
    download(url, type) {
      download(url, type)
    },
    //获取招标详情
    fileDetail() {
      uni.showLoading({})
      let params = {
        hostId: this.id,
        type: 1
      }
      this.$u.api.fileList(params).then(data => {
        if (data.success) {
          this.fileResult = data.data.records;
          uni.hideLoading()
        }
      }).catch(err => {
        this.$u.func.showToast({
          title: err,
        })
        uni.hideLoading()
      })
    },
    copyUrl() {
      uni.setClipboardData({
        data: this.result.url,
        success: function () {
          uni.showToast({
            title: '复制成功',
            icon: 'none'
          })
        },
      });
    },
    copyText(text) {
      uni.setClipboardData({
        data: text,
        success: function () {
          uni.showToast({
            title: '复制成功',
            icon: 'none'
          });
        }
      });
    },
    //获取招标详情
    getDetail() {
      uni.showLoading({})
      let params = {
        id: this.id,
        content: this.content ? this.content : '',
      }
      this.$u.api.purchaseDetail(params).then(data => {
        if (data.success) {
          this.result = data.data;
          uni.hideLoading()
        } else {
          uni.navigateTo({
            url: '/pages/login/login-account'
          })
        }
      }).catch(err => {
        this.$u.func.showToast({
          title: err,
        })
        uni.hideLoading()
      })
    },
  }
}
</script>

<style>
.wrap {
  margin-top: 12px;
}

.list {
  background: #fff;
  padding: 12px 12px 20px 12px;
  border-bottom: 1px dashed #D8D8D8;
}

.left-title {
  border-left: 2px solid #4075FF;
  padding-left: 4px;
}

.warnTitle {
  color: red;
  font-size: 26rpx;
  letter-spacing: 2px;
  padding: 20rpx;
  text-align: center;
  background: #fff;
  border-bottom: 1px dashed #efefef;
}
</style>