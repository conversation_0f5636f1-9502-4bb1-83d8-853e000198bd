<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF预览</title>
    <script src="https://static.yipintemian.com/pdf/pdf.min.js"></script>
    <script src="https://static.yipintemian.com/pdf/pdf.worker.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        body {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
        }

        body > input,
        body > div {
            display: block;
            width: 100%;
            margin-bottom: 10px;
        }

        #pdfViewer canvas {
            width: 100%;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>

<body>

<div id="pdfViewer"></div>
<p id="aa"></p>

<script>
  function getParameterByName(name) {
    name = name.replace(/[\[\]]/g, "\\&");
    let regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|)"),
      results = regex.exec(window.location.search);
    if (!results) return null;
    if (!results[2]) return '';
    return results[2].replace(/\+/g, " ");
  }
  const url = getParameterByName(url)
  document.getElementById('aa').value = url;
  window.onload = function () {
    const viewer = document.getElementById('pdfViewer');
    if (!url) {
      console.error('Please enter a URL.');
      return;
    }
    // 移除之前的canvas
    while (viewer.firstChild) {
      viewer.removeChild(viewer.firstChild);
    }
    pdfjsLib.getDocument(url).promise.then(function (pdf) {
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        pdf.getPage(pageNum).then(function (page) {
          const scale = 1.5;
          const viewport = page.getViewport({
            scale: scale
          });
          const canvas = document.createElement('canvas');
          const context = canvas.getContext('2d');
          canvas.height = viewport.height;
          canvas.width = viewport.width;
          viewer.appendChild(canvas);
          const renderContext = {
            canvasContext: context,
            viewport: viewport
          };
          page.render(renderContext);
        });
      }
    })
  }
</script>

</body>

</html>