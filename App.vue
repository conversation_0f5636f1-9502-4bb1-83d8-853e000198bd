<script>
export default {
  onLaunch: function () {
    const token = uni.getStorageSync('accessToken') || ''
    if (!token) {
      console.log('未登录')
    } else {
      console.log('已登录')
    }
  },
  methods: {
    checkForUpdate() {
      const _this = this
      // 检查小程序是否有新版本发布
      const updateManager = uni.getUpdateManager();
      // 请求完新版本信息的回调
      updateManager.onCheckForUpdate((res) => {
        console.log('onCheckForUpdate-res', res);
        //检测到新版本，需要更新，给出提示
        if (res && res.hasUpdate) {
          uni.showModal({
            title: '更新提示',
            content: '检测到新版本，是否下载新版本并重启小程序？',
            success(res) {
              if (res.confirm) {
                //用户确定下载更新小程序，小程序下载及更新静默进行
                _this.downLoadAndUpdate(updateManager)
              } else {
                // 若用户点击了取消按钮，二次弹窗，强制更新，如果用户选择取消后不需要进行任何操作，则以下内容可忽略
                uni.showModal({
                  title: '温馨提示~',
                  content: '本次版本更新涉及到新的功能添加，旧版本无法正常访问的哦~',
                  confirmText: "确定更新",
                  cancelText: "取消更新",
                  success(res) {
                    if (res.confirm) {
                      //下载新版本，并重新应用
                      _this.downLoadAndUpdate(updateManager)
                    }
                  }
                })
              }
            }
          })
        }
      })
    },
    // 下载小程序新版本并重启应用
    downLoadAndUpdate(updateManager) {
      uni.showLoading({title: '小程序更新中'});

      // 静默下载更新小程序新版本
      updateManager.onUpdateReady((res) => {
        console.log('onUpdateReady-res', res);
        uni.hideLoading();
        //新的版本已经下载好，调用 applyUpdate 应用新版本并重启
        updateManager.applyUpdate()
      });

      // 更新失败
      updateManager.onUpdateFailed((res) => {
        console.log('onUpdateFailed-res', res);
        // 新的版本下载失败
        uni.hideLoading();
        uni.showModal({
          title: '已经有新版本了哟~',
          content: '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~',
          showCancel: false
        });
      });
    }
  },
  onShow: function () {
    console.log('App Show')
    this.checkForUpdate()
  },
  onHide: function () {
    console.log('App Hide')
  }
}
</script>

<style lang="scss">
/*每个页面公共css */
@import "@/uni_modules/uview-ui/index.scss";
@import '@/uni_modules/uni-scss/index.scss';
@import '@/static/style/app.scss';
/* #ifndef APP-NVUE */
@import '@/static/style/customicons.css';
// 设置整个项目的背景色
page {
  background-color: #f5f5f5;
}

/* #endif */
.example-info {
  font-size: 14px;
  color: #333;
  padding: 10px;
}
</style>