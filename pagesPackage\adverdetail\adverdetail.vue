<template>
  <view class="wrap">
    <view v-if="result" class="list">
      <u-row customStyle="margin-bottom: 20px">
        <u-col span="3">
          <span>公司名称：</span>
        </u-col>
        <u-col span="9" customStyle="text-align: right;font-size:14px;color:#666">
          <span>{{ result.companyName }}</span>
        </u-col>
      </u-row>
      <!--<u-row customStyle="margin-bottom: 20px">
        <u-col span="3">
          <span>时间：</span>
        </u-col>
        <u-col span="9" customStyle="text-align: right;font-size:14px;color:#666">
          <span>{{result.createTime}}</span>
        </u-col>
      </u-row>-->
      <u-row customStyle="margin-bottom: 20px">
        <u-col span="3">
          <span>行业：</span>
        </u-col>
        <u-col span="9" customStyle="text-align: right;font-size:14px;color:#666">
          <span>{{ result.industry }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px">
        <u-col span="3">
          <span>品牌：</span>
        </u-col>
        <u-col span="9" customStyle="text-align: right;font-size:14px;color:#666">
          <span>{{ result.brand }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px">
        <u-col span="3">
          <span>联系人：</span>
        </u-col>
        <u-col span="9" customStyle="text-align: right;font-size:14px;color:#666">
          <span>{{ result.userName }}</span>
        </u-col>
      </u-row>
      <u-row customStyle="margin-bottom: 20px;">
        <u-col span="3">
          <span>联系方式：</span>
        </u-col>
        <u-col span="9" customStyle="text-align: right;font-size:14px;color:#666">
          <span>{{ result.userPhone }}</span>
        </u-col>
      </u-row>
      <view style="border-bottom: 1px dashed #D8D8D8;height: 16px;margin: 20px 0;"></view>
      <view style="font-size: 40rpx;text-align: center;font-weight: bold;margin: 18px 0 0 18px;color: #444;">
        {{ result.title }}
      </view>
      <!--<view style="color: #777;font-size: 28rpx;padding: 24rpx 0 40rpx 0;">发布时间：{{ createTime }}</view>-->
      <mp-html lazy-load scroll-table selectable use-anchor :content="result.content"></mp-html>
    </view>

    <view v-else style="padding-top: 40%;">
      <u-empty mode="list" text="暂无数据">
      </u-empty>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      html: '<div>Hello World!</div>',
      result: '',
      // createTime: ''
    }
  },
  onLoad(options) {
    console.log(options)
    this.getDetail(options.id)
  },
  methods: {
    //获取中表结果详情
    getDetail(id) {
      uni.showLoading({})
      let params = {
        id: id
      }
      this.$u.api.adverdetail(params).then(data => {
        if (data.success) {
          this.result = data.data;
          // this.createTime = data.data.createTime.slice(0, 10)
          uni.hideLoading()
          uni.setNavigationBarTitle({
            title: this.result.title
          })
        }
      }).catch(err => {
        console.log(err)
        this.$u.func.showToast({
          title: err,
        })
        uni.hideLoading()
      })
    },
  }
}
</script>

<style>
.wrap {
  margin-top: 12px;
  min-height: 100vh;
  background: #fff;
}

.list {
  background: #fff;
  padding: 12px;
  padding-bottom: 20px;
}

img {
  width: 100%;
}
</style>