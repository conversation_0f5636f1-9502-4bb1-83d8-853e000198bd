<template>
	<view class="u-page">
		<view v-if="result.length > 0" style="margin: 24rpx;">
			<view class="news-list" v-for="item in result" :key="item.id">
				<view @click="toDetail(item)" hover-class="none" class="news-item">
					<view v-if="item.demandImg[0].startsWith('http')" class="left-section">
						<image :src="item.demandImg[0]" class="item-image"></image>
					</view>
					<view class="right-section">
						<view class="top-section">
							<view class="info">
								{{ item.demandTitle }}
							</view>
						</view>
						<view class="bottom-section">
							<view class="date" v-if="item.createTime">
								<text>{{ item.createTime}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="no-more" v-if="result.length != 0">
				<u-loadmore :status="status" dashed line :fontSize="12" />
			</view>
		</view>

		<view style="padding-top:30%;" v-else>
			<u-empty mode="list" text="暂无数据">
			</u-empty>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				pages: 1,
				status: 'loadmore',
				query: {
					current: 1,
					size: 10,
				},
				result: [],
			}
		},
		onLoad() {
			if (!uni.getStorageSync('accessToken')) {
				uni.navigateTo({
					url: '/pages/login/login-account'
				})
			} else {
				if (this.query.current >= this.pages) {
					this.status = 'nomore';
				}
				this.pageSearch();
			}
		},
		onReachBottom() {
			this.status = 'loading';
			if (this.query.current >= this.pages) {
				this.status = 'nomore';
			} else {
				this.query.current++
				setTimeout(() => {
					this.pageSearch()
				}, 200)
			}
		},
		onShareAppMessage() {
			return {
				title: '',
				path: '/pagesPackage/my/demandHistory',
			}
		},
		methods: {
			toDetail(e) {
				this.$u.func.route('/pagesPackage/requirementRelease/requirementDetail?id=' + e.id)
			},
			pageSearch() {
				uni.showLoading({})
				this.$u.api.getDemandHistory(this.query).then(data => {
					if (data.success) {
						this.pages = data.data.pages
						let arr = data.data.records.map(item => {
							item.demandImg = item.demandImg.split(',')
							return item
						})
						if (arr.length == 0) {
							this.result = []
						} else {
							this.result = this.result.concat(...arr)
						}
						uni.hideLoading()
					} else {
						uni.showToast({
							title: data.data.msg,
							icon: 'none'
						});
					}
				}).catch(err => {
					console.log(err)
					uni.hideLoading()
					if (err.data.code === 401) {
						uni.showToast({
							title: '登录已过期，请重新登录！',
							duration: 3000,
							icon: 'none'
						})
						uni.navigateTo({
							url: '/pages/login/login-account'
						})
					} else {
						uni.showToast({
							title: err.data.msg,
							icon: 'none'
						});
					}
				})
			},
		}
	}
</script>

<style lang="scss">
	.news-list {
		margin-top: 30rpx;

		.news-item {
			&:not(:last-of-type) {
				padding: 0 0 30rpx;
				margin-bottom: 30rpx;
				border-bottom: 1px solid #eeeeee;
			}

			background: #fff;
			border-radius: 8rpx;
			padding: 16rpx;
			display: flex;
			height: 200rpx;

			.top-section {
				margin-bottom: 20rpx;

				.info {
					font-family: Source Han Sans;
					font-size: 26rpx;
					font-weight: normal;
					line-height: normal;
					letter-spacing: normal;
					color: #333333;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
				}
			}

			.left-section {
				width: 168rpx;
				height: 168rpx;
				margin-right: 24rpx;

				.item-image {
					width: 100%;
					height: 100%;
					border-radius: 8rpx;
				}
			}

			.right-section {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
			}

			.bottom-section {
				display: flex;
				justify-content: flex-end;

				.date {
					display: flex;
					align-items: center;
					font-family: Source Han Sans;
					font-size: 26rpx;
					font-weight: normal;
					line-height: normal;
					letter-spacing: normal;
					color: #333333;
				}
			}
		}
	}
</style>