<template>
	<web-view :src="jumpUrl"></web-view>
</template>
<script>
	export default {
		data() {
			return {
				jumpUrl: ''
			}
		},
		async onLoad(option) {
			this.jumpUrl = 'http://gcyj.hzdssoft.com/test.html?token=' + encodeURIComponent(uni.getStorageSync('accessToken'))
			console.log(this.jumpUrl)
			if (option && option.openid) {
				let userInfo = uni.getStorageSync('userInfo');
				userInfo = await this.$u.api.userInfo({
					id: userInfo.user_id
				})
				if (option.openId) {
					let res = await this.$u.api.updateUser({
						...userInfo,
						publicOpenId: option.openId
					})
					if (res.success) {
						uni.showToast({
							duration: 1000,
							title: '保存成功',
							icon: 'success'
						})
						setTimeout(function() {
							uni.switchTab({
								url: '/pages/home/<USER>'
							})
						}, 1000)
					}
				}
			}
		},
		methods: {}
	}
</script>