<template>
  <view>
    <view class='header'>
      <image class="my-img" src='https://pp.myapp.com/ma_icon/0/icon_10910_1720595013/256'></image>
    </view>
    <view class='content'>
      <view>申请获取以下权限</view>
      <text>获得你的公开信息(手机号，头像等)</text>
    </view>
    <authcheck @checkval="getcheck" @toagreement="toagreement"></authcheck>
    <br>
    <view class="login-box" style="width: 90%;margin: auto;">
      <u-button type="primary" color="#1aad19" shape="circle" @click="getinfo" text="授 权 登 录"></u-button>
    </view>
  </view>

</template>

<script>
import authcheck from '@/components/authcheck/authcheck.vue';

export default {
  components: {
    authcheck
  },
  data() {
    return {
      activeRadio: false, //勾选协议
      code: '',
      encryptedData: '',
      tenantId: '000000',
      iv: '',
      username: '',
      password: '',
      disabled: true,
      timer: null
    };
  },
  onUnload: function () {
    clearTimeout(this.timer);
  },
  onLoad() {
    wx.getSetting({
      success(res) {
        if (res.authSetting['scope.userInfo']) {
          // 已经授权，可以直接调用 getUserInfo 获取头像昵称
          wx.getUserInfo({
            success: function (res) {
              console.log('userInfo', res.userInfo)
            }
          })
        }
      }
    })
  },
  onShow() {
    uni.setStorageSync('accessToken', '')
    uni.setStorageSync('userInfo', '')
    this.$u.vuex('isLogin', false)
    this.$u.vuex('accessToken', '')
    this.$u.vuex('userInfo', '')
  },
  methods: {
    toagreement() {
      uni.navigateTo({
        url: '/pagesPackage/agreement/agreement'
      })
    },
    getcheck(e) {
      this.activeRadio = e;
    },
    getinfo() {
      let that = this
      if (!that.activeRadio) {
        uni.showToast({
          title: '请先阅读并同意协议',
          icon: 'none',
          duration: 2000
        });
      } else {
        uni.login({
          provider: "weixin",
          success: function (res) {
            if (res.errMsg == "login:ok") {
              that.code = res.code;
              that.userlogin();
            } else {
              //腾讯解密api出现失败的情况
              this.$toast({
                title: "授权失败了,请重新点击试试"
              });
            }
          }
        })
      }
    },
    getTokens(openId) {
      let params = {
        openId: openId,
        tenantId: '000000',
        grant_type: 'appUser'
      }
      this.$u.api.getToken(params).then(data => {
        //存在就存贮token
        if (data.access_token) {
          uni.hideLoading()
          uni.setStorageSync('userInfo', data)
          uni.setStorageSync('accessToken', data.access_token)
          this.$u.vuex('isLogin', true)
          this.$u.vuex('accessToken', data.access_token)
          this.$u.vuex('userInfo', data)
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/user/personalData'
            })
          }, 200)
        }
      }).catch(err => {
        uni.hideLoading()
        let result = err.data
        if (result.access_token) {
          uni.hideLoading()
          uni.setStorageSync('userInfo', result)
          uni.setStorageSync('accessToken', result.access_token)
          this.$u.vuex('isLogin', true)
          this.$u.vuex('accessToken', result.access_token)
          this.$u.vuex('userInfo', result)
          setTimeout(() => {
            uni.switchTab({
              url: '/pages/home/<USER>'
            })
          }, 200)
        }
        this.$u.func.showToast({
          title: err,
        })
      })
    },
    userlogin() {
      uni.showLoading({
        title: '登录中...'
      })
      let that = this
      let params = {
        code: this.code,
      }
      this.$u.api.decodeUserInfo(params).then(data => {
        if (data.success) {
          let openId = data.data
          that.getTokens(openId)
        } else {
          uni.hideLoading()
          uni.showToast({
            title: data.msg,
            icon: 'none',
            duration: 2000
          });
        }
      }).catch(() => {
        uni.hideLoading()
      })
    }
  }
}
</script>

<style lang="scss">
.header {
  margin: 90rpx 0 40rpx 50rpx;
  border-bottom: 1px solid #ccc;
  text-align: center;
  width: 650rpx;
  height: 300rpx;
  line-height: 450rpx;
}

.my-img {
  width: 120rpx;
  height: 120rpx;
}

.content {
  margin-left: 50rpx;
  margin-bottom: 40rpx;
}

.content text {
  display: block;
  color: #9d9d9d;
  margin-top: 40rpx;
}

.bottom {
  border-radius: 80rpx;
  margin: 70rpx 50rpx;
  font-size: 35rpx;
}

.login-box {
  margin-top: 40rpx;
}
</style>