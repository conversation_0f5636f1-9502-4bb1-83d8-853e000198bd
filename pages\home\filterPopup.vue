<template>
	<div class="filter-popup" v-if="visible">
		<div class="popup-mask" @click="closePopup"></div>
		<div class="popup-content">
			<div class="filter-content">
				<scroll-view class="filter-nav" scroll-y>
					<view v-for="(item, index) in filterData" :key="index" class="nav-item"
						:class="{ active: currentIndex === index }" @click="switchTab(index)">
						{{ item.title }}
						<view class="nav-dot" v-if="hasCategorySelected(index)"></view>
					</view>
				</scroll-view>

				<scroll-view class="filter-content-right" scroll-y scroll-with-animation
					:scroll-into-view="'item' + currentIndex" @scroll="handleScroll" ref="rightScroll">
					<view v-for="(item, index) in filterData" :key="index" :id="'item' + index" class="content-block">
						<view class="block-title">{{ item.title }}</view>
						<view v-if="item.code === 'time'" class="time-picker-container">
							<picker mode="date" :value="startTime" @change="bindStartDateChange" class="date-picker">
								<view class="uni-input">{{ startTime ? startTime : '开始时间' }}</view>
							</picker>
							<text class="time-separator">至</text>
							<picker mode="date" :value="endTime" @change="bindEndDateChange" class="date-picker">
								<view class="uni-input">{{ endTime ? endTime : '结束时间' }}</view>
							</picker>
						</view>
						<view v-else class="block-items">
							<view v-for="(subItem, subIndex) in item.children" :key="subIndex" class="item-tag"
								:class="{ active: isItemSelected(index, subItem.id) }"
								@click="selectItem(index, subItem)">
								{{ subItem.name }}
							</view>
						</view>
					</view>
				</scroll-view>
			</div>

			<div class="filter-btns">
				<view class="btn-reset" @click="resetFilter">重置</view>
				<view class="btn-confirm" @click="confirmFilter">确定</view>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'FilterPopup',
	props: {
		filterData: {
			type: Array,
			default: () => []
		},
		visible: {
			type: Boolean,
			default: false
		}
	},
	model: {
		prop: 'visible',
		event: 'update:visible'
	},
	data() {
		return {
			currentIndex: 0,
			selectedItems: {},
			sectionPositions: [],
			isScrolling: false,
			startTime: '',
			endTime: '',
			scrollTimer: null
		};
	},
	watch: {
		visible(val) {
			if (val) {
				this.$nextTick(() => {
					this.currentIndex = 0;
					this.calculateSectionPositions();
				});
			}
		}
	},
	mounted() {
		this.$nextTick(() => {
			this.calculateSectionPositions();
		});
	},
	methods: {
		closePopup() {
			this.$emit('update:visible', false);
			this.$emit('close');
		},

		switchTab(index) {
			this.currentIndex = index;
			this.isScrolling = true;

			this.$nextTick(() => {
				const id = `#item${index}`;
				const query = uni.createSelectorQuery().in(this);
				query.select(id).boundingClientRect().select('.filter-content-right').boundingClientRect()
					.exec(res => {
						if (res && res[0] && res[1] && this.$refs.rightScroll) {
							const scrollTop = res[0].top - res[1].top;
							this.$refs.rightScroll.scrollTop = scrollTop;
						}
					});

				setTimeout(() => {
					this.isScrolling = false;
				}, 500);
			});
		},

		calculateSectionPositions() {
			this.sectionPositions = [];

			setTimeout(() => {
				const query = uni.createSelectorQuery().in(this);

				query.select('.filter-content-right').boundingClientRect();

				this.filterData.forEach((_, index) => {
					query.select(`#item${index}`).boundingClientRect();
				});

				query.exec(res => {
					if (!res || !res[0]) return;

					const scrollViewRect = res[0];
					for (let i = 1; i < res.length; i++) {
						if (res[i]) {
							this.sectionPositions[i - 1] = res[i].top - scrollViewRect.top;
						}
					}
				});
			}, 50);
		},

		handleScroll(e) {
			if (this.isScrolling) return;

			const scrollTop = e.detail.scrollTop;

			if (!this.sectionPositions || this.sectionPositions.length === 0) {
				this.calculateSectionPositions();
				return;
			}

			if (this.scrollTimer) clearTimeout(this.scrollTimer);

			this.scrollTimer = setTimeout(() => {
				let newIndex = this.currentIndex;

				for (let i = 0; i < this.sectionPositions.length; i++) {
					const currentPosition = this.sectionPositions[i] || 0;
					const nextPosition = this.sectionPositions[i + 1] || Number.MAX_VALUE;

					if (scrollTop >= currentPosition - 5 && scrollTop < nextPosition - 5) {
						newIndex = i;
						break;
					}
				}

				if (this.currentIndex !== newIndex) {
					this.currentIndex = newIndex;
				}
			}, 100);
		},

		isItemSelected(categoryIndex, itemId) {
			const selectedIds = this.selectedItems[categoryIndex] || [];
			return selectedIds.includes(itemId);
		},

		hasCategorySelected(categoryIndex) {
			const selectedIds = this.selectedItems[categoryIndex] || [];
			return selectedIds.length > 0;
		},

		selectItem(categoryIndex, item) {
			const category = this.filterData[categoryIndex];
			if (!this.selectedItems[categoryIndex]) {
				this.$set(this.selectedItems, categoryIndex, []);
			}

			const selectedIds = this.selectedItems[categoryIndex];
			const itemIndex = selectedIds.indexOf(item.id);

			if (category.multiSelect) {
				if (itemIndex > -1) {
					selectedIds.splice(itemIndex, 1);
				} else {
					selectedIds.push(item.id);
				}
			} else {
				if (itemIndex > -1) {
					this.$set(this.selectedItems, categoryIndex, []);
				} else {
					this.$set(this.selectedItems, categoryIndex, [item.id]);
				}
			}
		},

		confirmFilter() {
			const formattedResult = {};
			Object.keys(this.selectedItems).forEach(categoryIndex => {
				const parentCode = this.filterData[categoryIndex].code;
				formattedResult[parentCode] = this.selectedItems[categoryIndex].join(',');
			});

			if (this.startTime) {
				formattedResult.startTime = this.startTime + ' 00:00:00';
			}
			if (this.endTime) {
				formattedResult.endTime = this.endTime + ' 23:59:59';
			}

			this.$emit('confirm', formattedResult);
			this.$emit('update:visible', false);
			this.$emit('close');
		},

		bindStartDateChange(e) {
			this.startTime = e.detail.value;
			if (this.endTime && this.endTime < this.startTime) {
				this.endTime = this.startTime;
				uni.showToast({
					title: '结束时间不能小于开始时间',
					icon: 'none'
				});
			}
		},

		bindEndDateChange(e) {
			const newEndTime = e.detail.value;
			if (this.startTime && newEndTime < this.startTime) {
				uni.showToast({
					title: '结束时间不能小于开始时间',
					icon: 'none'
				});
				this.endTime = this.startTime;
			} else {
				this.endTime = newEndTime;
			}
		},

		resetFilter() {
			this.selectedItems = {};
			this.startTime = '';
			this.endTime = '';
		}
	}
}
</script>

<style lang="scss" scoped>
.filter-popup {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 999;

	.popup-mask {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
	}

	.popup-content {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 55%;
		background: #fff;
		border-radius: 20rpx 20rpx 0 0;
		display: flex;
		flex-direction: column;
	}

	.filter-content {
		display: flex;
		flex: 1;
		overflow: hidden;
	}

	.filter-nav {
		width: 180rpx;
		height: 100%;
		background: #f5f6fa;

		.nav-item {
			height: 90rpx;
			line-height: 90rpx;
			text-align: center;
			font-size: 28rpx;
			color: #333;
			position: relative;

			.nav-dot {
				position: absolute;
				top: 10rpx;
				right: 10rpx;
				width: 16rpx;
				height: 16rpx;
				border-radius: 50%;
				background-color: #ff4d4f;
			}

			&.active {
				background: #fff;
				font-family: Source Han Sans;
				font-size: 26rpx;
				color: #325AEE;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}

	.filter-content-right {
		flex: 1;
		height: 100%;
		padding: 0 30rpx;

		.content-block {
			padding: 20rpx 0;

			.block-title {
				font-size: 30rpx;
				font-weight: bold;
				margin-bottom: 20rpx;
			}

			.time-picker-container {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;

				.date-picker {
					flex: 1;
					height: 70rpx;
					background-color: #f5f7fa;
					border-radius: 8rpx;

					.uni-input {
						height: 70rpx;
						line-height: 70rpx;
						padding: 0 20rpx;
						font-size: 26rpx;
						color: #333;
					}
				}

				.time-separator {
					margin: 0 15rpx;
					color: #999;
					font-size: 24rpx;
				}
			}

			.block-items {
				display: flex;
				flex-wrap: wrap;

				.item-tag {
					padding: 0 30rpx;
					height: 48rpx;
					line-height: 48rpx;
					border: 1px solid #eee;
					border-radius: 528rpx;
					margin: 0 20rpx 20rpx 0;
					font-family: Source Han Sans;
					font-size: 22rpx;
					letter-spacing: normal;
					color: #514F5C;
					background-color: #f5f6fa;

					&.active {
						color: #fff;
						background: #18b4fe;
						border-color: #18b4fe;
					}
				}
			}
		}
	}

	.filter-btns {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		height: 130rpx;
		padding: 32rpx 28rpx;
		box-sizing: border-box;

		.btn-reset,
		.btn-confirm {
			width: 336rpx;
			height: 66rpx;
			border-radius: 624rpx;
			border: none;
			font-family: Source Han Sans;
			font-size: 26rpx;
			font-weight: 600;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.btn-reset {
			color: #325AEE;
			background: #ecf2fe;
		}

		.btn-confirm {
			color: #fff;
			background: #325aee;
		}
	}
}
</style>