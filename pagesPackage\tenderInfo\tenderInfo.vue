<template>
  <view class="u-page">
    <view style="background: #fff;padding:20px 10px">
      <u-search placeholder="请输入内容" border="surround" v-model="query.queryKeyWord" :show-action="true"
                borderColor="rgb(230, 230, 230)" bgColor="#fff" shape="round" @custom="searchall"
                @search="searchall"></u-search>
      <view class="menu" style="display: flex;padding: 20rpx 0;font-size: 28rpx;">
        <view style="display: flex;flex:1;align-items: center;justify-content: center;">
          <view>建设工程</view>
        </view>

        <view class="flex-1">
          <!-- date -->
          <picker mode="date" :value="query.queryTime" @change="binddateChange">
            <view>{{ queryTime }}</view>
          </picker>
          <u-icon name="arrow-down" color="#777" size="15"></u-icon>
        </view>

        <view class="flex-1" @click="reset">
          重置
          <u-icon name="reload" color="#777" size="15"></u-icon>
        </view>
      </view>

      <u-row justify="space-between" gutter="5">
        <u-col span="3" v-for="item in biaoArr" :key="item.id">
          <view style="margin-top: 5px;">
            <view class="item-tabs" :class="item.name === biaoName?'active':''" @click="selectType(item)">
              {{ item.name }}
            </view>
          </view>
        </u-col>
      </u-row>

      <view class="model_scrollx flex_row">
        <scroll-view class="uni-swiper-tab" scroll-x>
          <view class="scrollx_items" v-for="item in areaArr" :key="item.code">
            <view class="item-tabs" @click="selectCity(item)" :class="item.code === cityCode?'active':''">
              {{ item.name }}
            </view>
          </view>
        </scroll-view>
      </view>

    </view>
    <view v-if="result.length>0" style="margin: 24rpx;">
      <view class="news-list" v-for="item in result" :key="item.id">
        <view @click="toDetail(item)" hover-class="none" class="news-item" v-if="item.projectName || item.name">
          <view class="right" style="display: flex;">
            <view style="display: flex;flex-direction: column;position: relative;">
              <view class="info">
                {{ item.projectName || item.name }}
              </view>
              <view v-if="item.region" class="region">
                <u-icon name="map" color="#0bb9c8" size="12"></u-icon>
                <span>{{ item.region }}</span>
              </view>
            </view>
            <view v-if="item.moneyTag == 1" class="number" style="color: #79baff;background: #ECF5FF;border: 1px solid #79baff;">
              1000万以下
            </view>
            <view v-if="item.moneyTag == 2" class="number" style="color: #97e16f;background: #effde5;border: 1px solid #9be378;">
              1000万-5000万
            </view>
            <view v-if="item.moneyTag == 3" class="number" style="color: #ecb56a;background: #FDF6EC;border: 1px solid #ecb56a;">
              5000万-1亿
            </view>
            <view v-if="item.moneyTag == 4" class="number" style="color: #ee7e7e;background: #FEF0F0;border: 1px solid #ee7e7e;">
              大于1亿
            </view>
            <view class="sum">{{ item.sum }}</view>
            <view class="date" style="display: flex;flex: 1;">
							<span style="position: absolute;right:0;bottom:0;display: flex;"
                    v-if="item.createTime || item.beginTime">
								<u-icon size="16" name="calendar" color="#a6abb5"></u-icon>
								{{ item.createTime || item.beginTime }}
							</span>
            </view>
          </view>
        </view>
      </view>
      <view class="no-more" v-if="result.length!=0">
        <u-loadmore :status="status" dashed line :fontSize="12"/>
      </view>
    </view>

    <view style="padding-top:30%;" v-else>
      <u-empty mode="list" text="暂无数据">
      </u-empty>
    </view>
    <!--    回到顶部 -->
    <view class="back-top-box">
      <u-back-top :scroll-top="scrollTop"></u-back-top>
    </view>
  </view>
</template>

<script>
import {cityArrs} from "@/utils/city";

export default {
  data() {
    return {
      scrollTop: 0,
      pages: 1,
      status: 'loadmore',
      queryTime: '日期',
      cityCode: '3328',
      query: {
        current: 1,
        size: 10,
        type: '',
        queryKeyWord: '',
        queryTime: '',
        region: ''
      },
      result: [],
      areaArr: cityArrs,
      biaoArr: [
        {
          id: 0,
          name: '招标公告'
        },
        {
          id: 1,
          name: '补充公告'
        },
        {
          id: 2,
          name: '中标候选人'
        },
        {
          id: 3,
          name: '中标结果'
        }
      ],
      array: [],
      statusValue: 0,
      areaValue: 0,
      biaoName: '招标公告',
      apiType: 0 //决定请求哪个接口
    }
  },
  onLoad() {
    if (!uni.getStorageSync('accessToken')) {
      uni.navigateTo({
        url: '/pages/login/login-account'
      })
    } else {
      if (this.query.current >= this.pages) {
        this.status = 'nomore';
      }
      this.switchMethod()
      this.getType()
    }
  },
  onReachBottom() {
    this.status = 'loading';
    if (this.query.current >= this.pages) {
      this.status = 'nomore';
    } else {
      this.query.current++
      setTimeout(() => {
        this.switchMethod()
      }, 200)
    }
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
  onShareAppMessage() {
    return {
      title: '',
      path: '/pagesPackage/tenderInfo/tenderInfo',
    }
  },
  onShareTimeline() {
    return {
      title: '',
      query: '',
    }
  },
  methods: {
    getType() {
      let params = {
        code: 'zb_type'
      }
      this.$u.api.getDictionary(params).then(res => {
        this.array = res.data
      });
    },
    searchall() {
      this.query.current = 1
      this.result = []
      this.switchMethod()
    },
    reset() {
      this.query = {
        current: 1,
        size: 10,
        queryKeyWord: '',
        queryTime: '',
        region: ''
      }
      this.queryTime = '日期'
      this.cityCode = '3328'
      this.statusValue = 0
      this.areaValue = 0
      this.biaoName = '招标公告'
      this.apiType = 0
      this.result = []
      this.pageSearch()
    },
    toDetail(e) {
      uni.showToast({
        title: this.apiType
      })
      switch (this.apiType) {
        case 0:
          //招标公告
          this.$u.func.route('/pagesPackage/searchDetail/searchDetail?id=' + e.id + '&name=' + e.projectName)
          break;
        case 1:
          //去补充公告详情
          this.$u.func.route('/pagesPackage/projectDetail/projectDetail?id=' + e.id + '&name=' + e.projectName)
          break;
        case 2:
          //中标候选人详情
          this.$u.func.route('/pagesPackage/candidateDetaill/candidateDetaill?id=' + e.id + '&name=' + e.projectName)
          break;
        case 3:
          //中标结果详情
          this.$u.func.route('/pagesPackage/resultDetail/resultDetail?id=' + e.id + '&name=' + e.projectName)
          break;
      }
    },
    switchMethod() {
      switch (this.apiType) {
        case 0:
          //招标公告
          this.pageSearch()
          break;
        case 1:
          //补充文件
          this.supplement()
          break;
        case 2:
          // 中标候选人
          this.candidatePage()
          break;
        case 3:
          //中标结果
          this.biaoResult()
          break;
      }
    },
    selectType(e) {
      this.biaoName = e.name
      this.apiType = e.id
      this.query.current = 1
      this.result = []
      this.switchMethod(this.apiType)
    },
    //中标结果公告分页
    biaoResult() {
      uni.showLoading({})
      this.$u.api.biaoResult(this.query).then(data => {
        if (data.success) {
          this.pages = data.data.pages
          let arr = data.data.records
          if (arr.length == 0) {
            this.result = []
          } else {
            this.result = this.result.concat(...arr)
          }
          uni.hideLoading()
        }
      }).catch(err => {
			uni.hideLoading()
			if (err.data.code === 401) {
			  uni.showToast({
			    title: '登录已过期，请重新登录！',
			    duration: 3000,
			    icon: 'none'
			  })
			  uni.navigateTo({
			    url: '/pages/login/login-account'
			  })
			}else{
				uni.showToast({
				  title: err.data.msg,
				  icon: 'none'
				});
				this.$u.func.showToast({
				  title: err,
				})
			}
      })
    },
    supplement() {
      uni.showLoading({})
      this.$u.api.supplement(this.query).then(data => {
        if (data.success) {
          this.pages = data.data.pages
          let arr = data.data.records
          if (arr.length == 0) {
            this.result = []
          } else {
            this.result = this.result.concat(...arr)
          }
          uni.hideLoading()
        }
      }).catch(err => {
		uni.hideLoading()
		if (err.data.code === 401) {
		  uni.showToast({
		    title: '登录已过期，请重新登录！',
		    duration: 3000,
		    icon: 'none'
		  })
		  uni.navigateTo({
		    url: '/pages/login/login-account'
		  })
		}else{
			uni.showToast({
			  title: err.data.msg,
			  icon: 'none'
			});
			this.$u.func.showToast({
			  title: err,
			})
		}
      })
    },
    candidatePage() {
      uni.showLoading({})
      this.$u.api.candidatePage(this.query).then(data => {
        if (data.success) {
          let arr = data.data.records
          if (arr.length == 0) {
            this.result = []
          } else {
            this.result = this.result.concat(...arr)
          }
          this.pages = data.data.pages
          uni.hideLoading()
        }
      }).catch(err => {
		uni.hideLoading()
		if (err.data.code === 401) {
		  uni.showToast({
		    title: '登录已过期，请重新登录！',
		    duration: 3000,
		    icon: 'none'
		  })
		  uni.navigateTo({
		    url: '/pages/login/login-account'
		  })
		}else{
			uni.showToast({
			  title: err.data.msg,
			  icon: 'none'
			});
			this.$u.func.showToast({
			  title: err,
			})
		}
      })
    },
    binddateChange: function (e) {
      this.query.current = 1
      this.query.queryTime = e.detail.value
      this.queryTime = e.detail.value
      this.result = []
      this.switchMethod(this.apiType)
    },
    selectCity(e) {
      this.cityCode = e.code
      this.query.current = 1
      this.result = []
      if (e.name === '全部' || e.name === '地区') {
        this.query.region = ''
      } else {
        this.query.region = e.name
      }
      this.switchMethod(this.apiType)
    },
    pageSearch() {
      uni.showLoading({})
      this.$u.api.purchasePage(this.query).then(data => {
        if (data.success) {
          this.pages = data.data.pages
          let arr = data.data.records
          if (arr.length == 0) {
            this.result = []
          } else {
            this.result = this.result.concat(...arr)
          }
          uni.hideLoading()
        } else {
          uni.showToast({
            title: data.data.msg,
            icon: 'none'
          });
        }
      }).catch(err => {
        uni.hideLoading()
        if (err.data.code === 401) {
          uni.showToast({
            title: '登录已过期，请重新登录！',
            duration: 3000,
            icon: 'none'
          })
          uni.navigateTo({
            url: '/pages/login/login-account'
          })
        }else{
        	uni.showToast({
        	  title: err.data.msg,
        	  icon: 'none'
        	});
        	this.$u.func.showToast({
        	  title: err,
        	})
        }
      })
    },
    bindPickChange(e) {
      this.query.current = 1
      this.statusValue = e.detail.value;
      this.query.type = this.array[this.statusValue].dictValue
      this.switchMethod(this.apiType)
    }
  }
}
</script>

<style lang="scss">
.news-list {
  margin-top: 30rpx;

  .news-item {
    &:not(:last-of-type) {
      padding: 0 0 30rpx;
      margin-bottom: 30rpx;
      border-bottom: 1px solid #eeeeee;
    }

    background: #fff;
    border-radius: 4px;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .right {
      flex: 1;
      height: 160rpx;
      display: flex;
      justify-content: space-around;
      margin-left: 10px;
      position: relative;

      .info {
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #585b61;
      }

      .date {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .icon {
          width: 21rpx;
          height: 21rpx;
          margin-right: 9rpx;
        }

        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #a6abb5;
      }
    }

    .img {
      flex-shrink: 0;
      width: 160rpx;
      height: 160rpx;
      border-radius: 10rpx;
      background-color: #82848a;
    }
  }
}

.uni-swiper-tab {
  white-space: nowrap;
}

.model_scrollx {
  justify-content: space-between;
  height: 45px;
  align-items: center;
}

.scrollx_items {
  text-align: center;
  display: inline-block;
  width: 110rpx;
  box-sizing: border-box;
  margin-right: 20rpx;
  margin-top: 3px;
}

.scrollx_items:last-child {
  margin-right: 30rpx;
}

.scrollx_items image {
  width: 70rpx;
  height: 66rpx;
}

.tgyx_title {
  font-size: 28rpx;
  color: #333333;
  margin-top: 30rpx;
}

.tgyx_desc {
  font-size: 24rpx;
  margin-top: 10rpx;
}

.line2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.active {
  color: #4075FF;
  background: #F1F5FF;
  border: 1px solid #F1F5FF !important
}

.item-tabs {
  margin-top: 10px;
}

.item-tabs {
  border-radius: 8rpx;
  border: 1px solid rgb(230, 230, 230);
  height: 54rpx;
  display: flex;
  font-size: 28rpx;
  align-items: center;
  justify-content: center;
}

.flex-1 {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
}

.list {
  background: #F7F7FA;
  padding: 10rpx;
  margin-bottom: 22rpx;
}

.flex-1 {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
}

.region {
  color: #0bb9c8;
  position: absolute;
  left: 0;
  bottom: 0;
  display: flex;
  font-size: 24rpx;
}

.number {
  position: absolute;
  bottom: 36rpx;
  left: 0;
  width: 180rpx;
  height: 32rpx;
  line-height: 32rpx;
  font-size: 24rpx;
  text-align: center;
  border-radius: 2px;
  font-weight: 500;
}

.sum {
  position: absolute;
  bottom: 36rpx;
  right: 0;
  width: 200rpx;
  height: 34rpx;
  line-height: 34rpx;
  font-size: 24rpx;
  text-align: right;
  border-radius: 2px;
  font-weight: 500;
  color: #000000;
}
</style>