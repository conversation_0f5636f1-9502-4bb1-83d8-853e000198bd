<template>
	<view class="u-page">
		<view class="search-container">
			<view class="search-top">
				<picker class="search-pick" mode="selector" :value="searchTypeIndex" :range="searchTypes" range-key="name"
					@change="bindSearchTypeChange">
					<view>{{ searchTypes[searchTypeIndex].name }}</view>
				</picker>
				<u-icon name="arrow-down" color="#777" size="15"></u-icon>
				<u-search placeholder="请输入内容" border="surround" v-model="searchValue" :show-action="true"
					borderColor="rgb(230, 230, 230)" bgColor="#fff" shape="round" @custom="searchall"
					@search="searchall"></u-search>
			</view>

			<view class="model_scrollx flex_row">
				<scroll-view class="uni-swiper-tab" scroll-x>
					<view class="scrollx_items" v-for="item in areaArr" :key="item.code">
						<view class="item-tabs" @click="selectCity(item)"
							:class="item.code === cityCode ? 'active' : ''">
							{{ item.name }}
						</view>
					</view>
				</scroll-view>
			</view>
			<view class="model_scrollx flex_row">
				<scroll-view class="uni-swiper-tab" scroll-x>
					<view class="scrollx_items" v-for="item in moneyArr" :key="item.id">
						<view class="item-tabs" @click="selectType(item)" :class="item.id === moneyTag ? 'active' : ''">
							{{ item.name }}
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
		<view v-if="result.length > 0" style="margin: 24rpx;">
			<view class="news-list" v-for="item in result" :key="item.id">
				<view @click="toDetail(item)" hover-class="none" class="news-item" v-if="item.projectName || item.name">
					<view class="right" style="display: flex;">
						<view style="display: flex;flex-direction: column;position: relative;">
							<view class="info">
								{{ item.projectName || item.name }}
							</view>
							<view v-if="item.region" class="region">
								<u-icon name="map" color="#0bb9c8" size="12"></u-icon>
								<span>{{ item.region }}</span>
							</view>
						</view>
						<view v-if="item.status == 1" class="number"
							style="color: #79baff;background: #ECF5FF;border: 1px solid #79baff;">
							1000万以下
						</view>
						<view v-if="item.status == 2" class="number"
							style="color: #97e16f;background: #effde5;border: 1px solid #9be378;">
							1000万-5000万
						</view>
						<view v-if="item.status == 3" class="number"
							style="color: #ecb56a;background: #FDF6EC;border: 1px solid #ecb56a;">
							5000万-1亿
						</view>
						<view v-if="item.status == 4" class="number"
							style="color: #ee7e7e;background: #FEF0F0;border: 1px solid #ee7e7e;">
							大于1亿
						</view>
						<view class="sum">{{ item.sum ? item.sum + '元' : '' }}</view>
						<view class="date" style="display: flex;flex: 1;">
							<span style="position: absolute;right:0;bottom:0;display: flex;"
								v-if="item.createTime || item.beginTime">
								<u-icon size="16" name="calendar" color="#a6abb5"></u-icon>
								{{ item.createTime || item.beginTime }}
							</span>
						</view>
					</view>
				</view>
			</view>
			<view class="no-more" v-if="result.length != 0">
				<u-loadmore :status="status" dashed line :fontSize="12" />
			</view>
		</view>

		<view style="padding-top:30%;" v-else>
			<u-empty mode="list" text="暂无数据">
			</u-empty>
		</view>
		<!--    回到顶部 -->
		<view class="back-top-box">
			<u-back-top :scroll-top="scrollTop"></u-back-top>
		</view>
	</view>
</template>

<script>
	import {
		cityArrs
	} from "@/utils/city";

	export default {
		data() {
			return {
				scrollTop: 0,
				pages: 1,
				status: 'loadmore',
				cityCode: '3328',
				searchTypes: [
					{ name: '项目名称', value: 'name' },
					{ name: '招标单位', value: 'bidPop' },
					// { name: '中标单位', value: 'winPop' }
				],
				searchTypeIndex: 0,
				searchValue: '',
				query: {
					current: 1,
					size: 10,
					region: '',
					name: '' // 默认使用项目名称搜索
				},
				result: [],
				areaArr: cityArrs,
				moneyArr: [{
						id: 0,
						name: '全部'
					}, {
						id: 1,
						name: '1000万以下'
					},
					{
						id: 2,
						name: '1000万-5000万'
					},
					{
						id: 3,
						name: '5000万-1亿'
					},
					{
						id: 4,
						name: '大于1亿'
					}
				],
				statusValue: 0,
				areaValue: 0,
				moneyTag: 0,
				apiType: 0 //决定请求哪个接口
			}
		},
		onLoad() {
			if (!uni.getStorageSync('accessToken')) {
				uni.navigateTo({
					url: '/pages/login/login-account'
				})
			} else {
				if (this.query.current >= this.pages) {
					this.status = 'nomore';
				}
				this.pageSearch()
			}
			if (this.userInfo.userLevel !== 2) {
				this.searchTypes = this.searchTypes.concat([
					{ name: '姓名', value: 'bidPopName' },
					{ name: '联系方式', value: 'bidPopPhone' }
				])
			}
		},
		onReachBottom() {
			this.status = 'loading';
			if (this.query.current >= this.pages) {
				this.status = 'nomore';
			} else {
				this.query.current++
				setTimeout(() => {
					this.pageSearch()
				}, 200)
			}
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		onShareAppMessage() {
			return {
				title: '',
				path: '/pagesPackage/addressBook/addressBook',
			}
		},
		onShareTimeline() {
			return {
				title: '',
				query: '',
			}
		},
		methods: {
			searchall() {
				this.query.current = 1;
				// 更新当前选中类型的字段值
				const selectedType = this.searchTypes[this.searchTypeIndex].value;
				// 清除之前的搜索字段，只保留基本字段
				this.query = {
					...this.query,
					name: '',
					bidPop: '',
					winPop: '',
					bidPopName: '',
					bidPopPhone: ''
				};
				// 设置当前选择的搜索字段
				this.query[selectedType] = this.searchValue;
				this.result = [];
				this.pageSearch();
			},
			reset() {
				this.query = {
					current: 1,
					size: 10,
					region: '',
					name: '', // 默认使用项目名称搜索
					bidPop: '',
					winPop: '',
					bidPopName: '',
					bidPopPhone: ''
				}
				this.searchValue = '';
				this.cityCode = '3328'
				this.statusValue = 0
				this.areaValue = 0
				this.searchTypeIndex = 0 // 重置搜索类型索引
				this.moneyTag = 0
				this.apiType = 0
				this.result = []
				this.pageSearch()
			},
			toDetail(e) {
				this.$u.func.route('/pagesPackage/addressBook/addressBookDetail?id=' + e.id)
			},
			selectType(e) {
				this.moneyTag = e.id
				this.query.current = 1
				this.query.status = e.id || ''
				this.result = []
				this.pageSearch()
			},
			bindSearchTypeChange: function(e) {
				this.searchTypeIndex = e.detail.value;
				const selectedType = this.searchTypes[this.searchTypeIndex].value;
				// 清除之前的搜索字段，只保留基本字段
				this.query = {
					...this.query,
					name: '',
					bidPop: '',
					winPop: '',
					bidPopName: '',
					bidPopPhone: ''
				};
				// 设置当前选择的搜索字段
				this.query[selectedType] = this.searchValue;
				this.query.current = 1;
				this.result = [];
				this.pageSearch();
			},
			selectCity(e) {
				this.cityCode = e.code
				this.query.current = 1
				this.result = []
				if (e.name === '全部' || e.name === '地区') {
					this.query.region = ''
				} else {
					this.query.region = e.name
				}
				this.pageSearch()
			},
			pageSearch() {
				uni.showLoading({})
				this.$u.api.projectPage(this.query).then(data => {
					if (data.success) {
						this.pages = data.data.pages
						let arr = data.data.records
						if (arr.length == 0) {
							this.result = []
						} else {
							this.result = this.result.concat(...arr)
						}
						uni.hideLoading()
					} else {
						uni.showToast({
							title: data.data.msg,
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading()
					if (err.data.code === 401) {
						uni.showToast({
							title: '登录已过期，请重新登录！',
							duration: 3000,
							icon: 'none'
						})
						uni.navigateTo({
							url: '/pages/login/login-account'
						})
					} else {
						uni.showToast({
							title: err.data.msg,
							icon: 'none'
						});
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.search-container {
		background: #fff;
		padding: 20px 10px
	}

	.search-top {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.search-pick {
		font-size: 24rpx;
	}

	.news-list {
		margin-top: 30rpx;

		.news-item {
			&:not(:last-of-type) {
				padding: 0 0 30rpx;
				margin-bottom: 30rpx;
				border-bottom: 1px solid #eeeeee;
			}

			background: #fff;
			border-radius: 4px;
			padding: 10px;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.right {
				flex: 1;
				height: 160rpx;
				display: flex;
				justify-content: space-around;
				margin-left: 10px;
				position: relative;

				.info {
					min-width: 0;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #585b61;
				}

				.date {
					display: flex;
					align-items: center;
					justify-content: flex-end;

					.icon {
						width: 21rpx;
						height: 21rpx;
						margin-right: 9rpx;
					}

					font-size: 26rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #a6abb5;
				}
			}

			.img {
				flex-shrink: 0;
				width: 160rpx;
				height: 160rpx;
				border-radius: 10rpx;
				background-color: #82848a;
			}
		}
	}

	.uni-swiper-tab {
		white-space: nowrap;
	}

	.model_scrollx {
		justify-content: space-between;
		height: 45px;
		align-items: center;
	}

	.scrollx_items {
		text-align: center;
		display: inline-block;
		min-width: 100rpx;
		box-sizing: border-box;
		margin-right: 20rpx;
		margin-top: 3px;
	}

	.scrollx_items:last-child {
		margin-right: 30rpx;
	}

	.scrollx_items image {
		width: 70rpx;
		height: 66rpx;
	}

	.tgyx_title {
		font-size: 28rpx;
		color: #333333;
		margin-top: 30rpx;
	}

	.tgyx_desc {
		font-size: 24rpx;
		margin-top: 10rpx;
	}

	.line2 {
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	.active {
		color: #4075FF;
		background: #F1F5FF;
		border: 1px solid #F1F5FF !important
	}

	.item-tabs {
		margin-top: 10px;
	}

	.item-tabs {
		border-radius: 8rpx;
		border: 1px solid rgb(230, 230, 230);
		height: 54rpx;
		display: flex;
		font-size: 28rpx;
		align-items: center;
		justify-content: center;
		padding: 0 10rpx;
	}

	.list {
		background: #F7F7FA;
		padding: 10rpx;
		margin-bottom: 22rpx;
	}

	.flex-1 {
		display: flex;
		flex: 1;
		align-items: center;
		justify-content: center;
	}

	.region {
		color: #0bb9c8;
		position: absolute;
		left: 0;
		bottom: 0;
		display: flex;
		font-size: 24rpx;
	}

	.number {
		position: absolute;
		bottom: 36rpx;
		left: 0;
		width: 180rpx;
		height: 32rpx;
		line-height: 32rpx;
		font-size: 24rpx;
		text-align: center;
		border-radius: 2px;
		font-weight: 500;
	}

	.sum {
		position: absolute;
		bottom: 36rpx;
		right: 0;
		width: 200rpx;
		height: 34rpx;
		line-height: 34rpx;
		font-size: 24rpx;
		text-align: right;
		border-radius: 2px;
		font-weight: 500;
		color: #000000;
	}
</style>