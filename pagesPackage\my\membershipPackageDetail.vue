<template>
    <div class="package-detail-container">
        <!-- 套餐名称部分 -->
        <div class="package-header">
            <div class="title-label">套餐名称</div>
            <div class="package-name">{{packageInfo.packageName}}</div>
        </div>
        
        <!-- 分隔线 -->
        <div class="divider"></div>
        
        <!-- 权益详情部分 -->
        <div class="benefits-section">
            <div class="benefits-title">权益详情</div>
            <div class="benefits-content">
                <mp-html lazy-load scroll-table selectable use-anchor :content="packageInfo.packageDesc"></mp-html>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                packageInfo: {}
            }
        },
        onLoad(options) {
            // 获取上一个页面传递的数据
            console.log(options.id);
            if (options.id) {
                try {
                    this.$u.api.getPackageDetail({
                        id: options.id
                    }).then(res => {
                        this.packageInfo = res.data;
                        
                        if (this.packageInfo.packageDesc) {
                            this.packageInfo.packageDesc = this.packageInfo.packageDesc.replace(/<img(?![^>]*style=)[^>]*>/gi, function(match) {
                                return match.replace(/<img/, '<img style="max-width:100%"');
                            });
                            
                            this.packageInfo.packageDesc = this.packageInfo.packageDesc.replace(/<img[^>]*style=["'][^"']*["'][^>]*>/gi, function(match) {
                                if (match.indexOf('max-width') === -1) {
                                    return match.replace(/style=["']([^"']*)["']/, function(styleAttr, styleValue) {
                                        return 'style="' + styleValue + ';max-width:100%"';
                                    });
                                }
                                return match;
                            });
                        }
                    })
                } catch (e) {
                    console.error('数据解析失败', e);
                    uni.showToast({
                        title: '数据加载失败',
                        icon: 'none'
                    });
                }
            }
        }
    }
</script>

<style lang="scss" scoped>
    .package-detail-container {
        padding: 40rpx;
        background-color: #f8f9fc;
        min-height: 100vh;
        
        .package-header {
            display: flex;
            align-items: center;
            padding: 48rpx 32rpx;
            background-color: #fff;
            border-radius: 8rpx;
            box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
            margin-bottom: 40rpx;
            
            .title-label {
                font-size: 32rpx;
                color: #666;
                font-weight: 500;
                margin-right: 40rpx;
                min-width: 140rpx;
            }
            
            .package-name {
                font-size: 36rpx;
                color: #333;
                font-weight: bold;
            }
        }
        
        .divider {
            height: 2rpx;
            background-color: #eee;
            margin: 30rpx 0;
            display: none; // 隐藏分隔线，使用卡片式设计代替
        }
        
        .benefits-section {
            padding: 48rpx 32rpx;
            background-color: #fff;
            border-radius: 8rpx;
            box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
            
            .benefits-title {
                font-size: 32rpx;
                color: #333;
                font-weight: bold;
                margin-bottom: 40rpx;
                position: relative;
                padding-left: 24rpx;
                
                &::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 8rpx;
                    height: 32rpx;
                    background: linear-gradient(to bottom, #3b7aff, #6ea8ff);
                    border-radius: 8rpx;
                }
            }
            
            .benefits-content {
                min-height: 600rpx;
                background-color: #f9f9f9;
                padding: 40rpx;
                font-size: 28rpx;
                color: #555;
                line-height: 1.6;
                border-radius: 8rpx;
            }
        }
    }
</style>