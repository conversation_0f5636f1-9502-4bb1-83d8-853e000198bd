<template>
	<view class="u-page">
		<view class="search-container">
			<view class="model_scrollx flex_row">
				<scroll-view class="uni-swiper-tab" scroll-x>
					<view class="scrollx_items" v-for="item in categoryList" :key="item.id">
						<view class="item-tabs" @click="selectCategory(item)"
							:class="item.id === query.categoryId ? 'active' : ''">
							{{ item.categoryName }}
						</view>
					</view>
				</scroll-view>
				<image src="/static/icon/filter.png" mode="scaleToFill" class="filter-icon" @click="showMoreFilter" />
			</view>
		</view>
		<view v-if="result.length > 0" style="margin: 24rpx;">
			<view class="news-list" v-for="item in result" :key="item.id">
				<view @click="toDetail(item)" hover-class="none" class="news-item">
					<view v-if="item.demandImg[0].startsWith('http')" class="left-section">
						<image :src="item.demandImg[0]" class="item-image"></image>
					</view>
					<view class="right-section">
						<view class="top-section">
							{{ item.demandTitle }}
						</view>
						<view class="bottom-section">
							<view class="date" v-if="item.publishTime">
								<text>{{ item.publishTime.split(' ')[0] }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="no-more" v-if="result.length != 0">
				<u-loadmore :status="status" dashed line :fontSize="12" />
			</view>
		</view>

		<view style="padding-top:30%;" v-else>
			<u-empty mode="list" text="暂无数据">
			</u-empty>
		</view>
		<view class="add-box">
			<image src="/static/icon/plus.png" mode="scaleToFill" class="add-icon" @click="addDemand"></image>
		</view>
		<!--    回到顶部 -->
		<view class="back-top-box">
			<u-back-top :scroll-top="scrollTop"></u-back-top>
		</view>
		<filter-popup :visible.sync="filterVisible" :filterData="filterData" @confirm="handleFilterConfirm"
			@close="filterVisible = false">
		</filter-popup>
	</view>
</template>

<script>
	import FilterPopup from './filterPopup.vue';

	export default {
		components: {
			FilterPopup
		},
		data() {
			return {
				scrollTop: 0,
				pages: 1,
				status: 'loadmore',
				query: {
					current: 1,
					size: 10,
					categoryId: '',
				},
				result: [],
				categoryList: [],
				filterVisible: false, // 控制筛选弹窗的显示
				filterData: [] // 初始化为空数组
			}
		},
		onShow() {
				this.query.current = 1
				this.result = []
				this.pageSearch()
				this.getCategoryList()
				this.getFilterData() // 获取筛选数据
		},
		onPullDownRefresh() {
			// 下拉刷新
			this.query.current = 1
			this.result = []
			this.pageSearch()
			this.getCategoryList()
			this.getFilterData()
			setTimeout(() => {
				uni.stopPullDownRefresh()
			}, 1000)
		},
		onReachBottom() {
			this.status = 'loading';
			if (this.query.current >= this.pages) {
				this.status = 'nomore';
			} else {
				this.query.current++
				setTimeout(() => {
					this.pageSearch()
				}, 200)
			}
		},
		methods: {
			addDemand() {
				this.$u.api.userInfo({
					id: this.userInfo.user_id
				}).then(data => {
					if (data.code === 200) {
						const row = Object.assign({}, this.userInfo, data.data)
						this.$u.vuex('userInfo', row)
					}
					if (this.userInfo.userLevel == 2) {
						uni.showToast({
							title: '仅认证用户可使用',
							icon: 'none'
						})
						return
					}
					if (!this.userInfo.companyName || !this.userInfo.userName || !this.userInfo.userPhone || !this.userInfo.publisherType || !this.userInfo.companyPost) {
						uni.navigateTo({
							url: '/pages/user/personalData'
						})
						return
					}
					this.$u.func.route('/pagesPackage/requirementRelease/addRequirement')
				})
			},
			getCategoryList() {
				this.$u.api.getCategoryList(1, -1).then(data => {
					this.categoryList = data.data.records.filter(item => item.enableStatus)
					this.categoryList.unshift({
						id: '',
						categoryName: '全部'
					})
				})
			},
			// 获取筛选数据
			getFilterData() {
				// 需要获取的字典类型列表
				const dictCodes = ['publisher_type', 'major', 'job_title', 'engineering_qualification', 'certificate'];

				// 字典类型对应的标题映射
				const titleMap = {
					'publisher_type': '发布者属性',
					'major': '专业',
					'job_title': '职称',
					'engineering_qualification': '工程资质',
					'certificate': '证书'
				};
				const codeMap = {
					'publisher_type': 'publisherType',
					'major': 'major',
					'job_title': 'jobTitle',
					'engineering_qualification': 'qualification',
					'certificate': 'certificateTitle'
				};
				// 为每个dictCode创建请求Promise
				const promises = dictCodes.map(code => {
					return this.$u.api.getDictionaryBiz({
							code
						})
						.then(res => {
							if (res.data && res.data.length > 0) {
								return {
									code,
									data: {
										title: titleMap[code] || code,
										code: codeMap[code],
										multiSelect: code === 'major' ? true : false,
										children: res.data.map(dict => ({
											id: dict.dictKey,
											name: dict.dictValue
										}))
									}
								};
							}
							return {
								code,
								data: null
							};
						})
						.catch(err => {
							console.error(`获取${code}字典数据失败:`, err);
							return {
								code,
								data: null
							};
						});
				});

				// 等待所有请求完成后，按原始顺序构建filterData
				Promise.all(promises).then(results => {
					this.filterData = results
						.filter(item => item.data !== null)
						.map(item => item.data);
					this.filterData.push({
						title: '发布时间',
						code: 'time',
						children: []
					});
				});
			},
			showMoreFilter() {
				// 显示筛选弹窗
				this.filterVisible = true;
			},
			toDetail(e) {
				this.$u.func.route(`/pagesPackage/requirementRelease/requirementDetail?id=${e.id}&openType=look`)
			},
			selectCategory(e) {
				this.query.categoryId = e.id
				this.query.current = 1
				this.result = []
				this.pageSearch()
			},
			pageSearch() {
				uni.showLoading({})
				this.$u.api.demandApp(this.query).then(data => {
					if (data.success) {
						this.pages = data.data.pages
						let arr = data.data.records.map(item => {
							item.demandImg = item.demandImg.split(',')
							return item
						})
						if (arr.length == 0) {
							this.result = []
						} else {
							this.result = this.result.concat(...arr)
						}
						if (this.query.current >= this.pages) {
							this.status = 'nomore';
						} else {
							this.status = 'loadmore';
						}
						uni.hideLoading()
					} else {
						uni.showToast({
							title: data.data.msg,
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading()
						uni.showToast({
							title: err.data.msg,
							icon: 'none'
						});
				})
			},
			handleFilterConfirm(selectedItems) {
				// 处理筛选数据
				console.log('筛选条件:', selectedItems);
				this.query = {
					current: this.query.current,
					size: this.query.size,
					categoryId: this.query.categoryId,
					...selectedItems
				}

				this.filterVisible = false;
				// 根据筛选条件重新查询数据
				this.query.current = 1;
				this.result = [];
				this.pageSearch();
			}
		}
	}
</script>

<style lang="scss" scoped>
	view {
		box-sizing: border-box;
	}

	.search-container {
		background: #fff;
		padding: 38rpx 24rpx;
	}

	.news-list {
		margin-top: 30rpx;

		.news-item {
			&:not(:last-of-type) {
				padding: 0 0 30rpx;
				margin-bottom: 30rpx;
				border-bottom: 1px solid #eeeeee;
			}

			background: #fff;
			border-radius: 8rpx;
			padding: 16rpx;
			display: flex;
			height: 200rpx;

			.top-section {
				margin-bottom: 20rpx;
				width: 100%;
				font-family: Source Han Sans;
				font-size: 26rpx;
				font-weight: normal;
				line-height: 1.3;
				letter-spacing: normal;
				color: #333333;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				word-break: break-all;
			}

			.left-section {
				width: 168rpx;
				height: 168rpx;
				margin-right: 24rpx;

				.item-image {
					width: 100%;
					height: 100%;
					border-radius: 8rpx;
				}
			}

			.right-section {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				padding: 8rpx 0 4rpx 0;
			}

			.bottom-section {
				display: flex;
				justify-content: flex-end;

				.date {
					display: flex;
					align-items: center;
					font-family: Source Han Sans;
					font-size: 26rpx;
					font-weight: normal;
					line-height: normal;
					letter-spacing: normal;
					color: #333333;
				}
			}
		}
	}

	.uni-swiper-tab {
		width: 90%;
		white-space: nowrap;
	}

	.model_scrollx {
		display: flex;
		justify-content: space-between;
		height: 45px;
		align-items: center;
	}

	.filter-icon {
		width: 32rpx;
		height: 32rpx;
	}

	.scrollx_items {
		text-align: center;
		display: inline-block;
		min-width: 100rpx;
		box-sizing: border-box;
		margin-right: 16rpx;
	}

	.scrollx_items image {
		width: 70rpx;
		height: 66rpx;
	}

	.active {
		color: #4075FF;
		background: #F1F5FF;
		border: 1px solid #F1F5FF !important
	}

	.item-tabs {
		border-radius: 8rpx;
		border: 1px solid rgb(230, 230, 230);
		height: 54rpx;
		display: flex;
		font-size: 28rpx;
		align-items: center;
		justify-content: center;
		padding: 0 10rpx;
	}

	.add-box {
		position: fixed;
		bottom: 0;
		right: 0;

		.add-icon {
			width: 124rpx;
			height: 124rpx;
		}
	}
</style>