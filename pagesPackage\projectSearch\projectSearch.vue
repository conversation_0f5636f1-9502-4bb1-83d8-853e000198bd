<template>
  <view class="u-page">
    <view style="background: #fff;padding: 10px 10px 0 10px;margin-bottom: 20rpx;">
      <view class="title">品牌表可通过预览或下载招标文件查询</view>
      <u-search placeholder="请输入品牌或项目名称" border="surround" v-model="query.content" :show-action="true"
                borderColor="rgb(230, 230, 230)" bgColor="#fff" shape="round" @custom="searchall"
                @search="searchall"></u-search>
      <view class="menu" style="display: flex;padding: 10px 0;font-size: 24rpx;">
        <view class="flex-1" style="flex: 2;">
          <picker mode="selector" :range="area" range-key="name" :value="query.area" @change="areaChange">
            <view>{{ area[areaValue].name }}</view>
          </picker>
          <u-icon name="arrow-down" color="#777" size="15"></u-icon>
        </view>
        <view class="flex-1" style="flex: 2;">
          项目名称
        </view>
        <view class="flex-1" style="flex: 2;">
          <!-- date -->
          <picker mode="date" :value="query.date" @change="binddateChange">
            <view>{{ query.date ? query.date : '日期' }}</view>
          </picker>
          <u-icon name="arrow-down" color="#777" size="15"></u-icon>
        </view>
        <view class="flex-1" @click="reset">
          <u-icon name="reload" color="#777" size="15"></u-icon>
          重置
        </view>
      </view>
    </view>

    <view>
      <view v-if="result.length > 0">
        <view class="news-list" v-for="item in result" :key="item.id">
          <view @click="toH5(item)" hover-class="none" class="news-item">
            <view class="right" style="display: flex;">
              <view>
                <view class="info">
                  {{ item.projectName }}
                </view>
                <view v-if="item.region" class="region">
                  <u-icon name="map" color="#0bb9c8" size="12"></u-icon>
                  <span>{{ item.region }}</span>
                </view>
              </view>
              <view class="date" style="display: flex;flex: 1;">
                <view v-if="item.sumTag == 1" class="number" style="color: #79baff;background: #ECF5FF;border: 1px solid #79baff;">
                  1000万以下
                </view>
                <view v-if="item.sumTag == 2" class="number" style="color: #97e16f;background: #effde5;border: 1px solid #9be378;">
                  1000万-5000万
                </view>
                <view v-if="item.sumTag == 3" class="number" style="color: #ecb56a;background: #FDF6EC;border: 1px solid #ecb56a;">
                  5000万-1亿
                </view>
                <view v-if="item.sumTag == 4" class="number" style="color: #ee7e7e;background: #FEF0F0;border: 1px solid #ee7e7e;">
                  大于1亿
                </view>
                <view class="sum">{{ item.sum }}</view>
                <span style="position: absolute;right: 0;bottom: 0;display: flex;">
									<u-icon size="16" name="calendar" color="#a6abb5"></u-icon>
									{{ item.beginTime ? item.beginTime.substring(0, 10) : '' }}
								</span>
              </view>
            </view>
          </view>
        </view>
        <view class="no-more">
          <u-loadmore :status="status" dashed line :fontSize="12"/>
        </view>
      </view>

      <view style="padding-top: 40%;" v-else>
        <u-empty mode="list" text="暂无数据"/>
      </view>
    </view>
    <!-- 回到顶部 -->
    <view class="back-top-box">
      <u-back-top :scroll-top="scrollTop"/>
    </view>
  </view>
</template>

<script>
import {cityArr} from "@/utils/city";

export default {
  data() {
    return {
      scrollTop: 0,
      pages: 1,
      status: 'loadmore',
      result: [],
      // 1招标公告 2中标候选人 3中标公告
      area: cityArr,
      array: [{
        id: 0,
        name: "公告类型"
      }, {
        id: 1,
        name: "招标公告"
      },
        {
          id: 2,
          name: "中标候选人"
        },
        {
          id: 3,
          name: "中标公告"
        }
      ],
      statusValue: 0,
      areaValue: 0,
      areaIndex: 0,
      query: {
        current: 1,
        size: 10,
        content: '',
        type: '', //【1招标公告 2中标候选人 3中标公告】
        region: '',
        date: ''
      },
    }
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
  onLoad(options) {
    if (!uni.getStorageSync('accessToken')) {
      uni.navigateTo({
        url: '/pages/login/login-account'
      })
    } else {
      this.getDetail();
    }
  },
  onReachBottom() {
    this.status = 'loading';
    if (this.query.current >= this.pages) {
      this.status = 'nomore';
    } else {
      this.query.current++
      setTimeout(() => {
        this.getDetail()
      }, 200)
    }
  },
  onShareAppMessage() {
    return {
      title: '',
      path: '/pagesPackage/projectSearch/projectSearch',
    }
  },
  onShareTimeline() {
    return {
      title: '',
      query: '',
    }
  },
  methods: {
    searchall() {
      this.query.current = 1
      this.result = []
      this.getDetail()
    },
    reset() {
      this.statusValue = 0
      this.areaValue = 0
      this.areaIndex = 0
      this.query = {
        current: 1,
        size: 10,
        content: '',
        type: '', //【1招标公告 2中标候选人 3中标公告】
        region: '',
        date: ''
      }
      this.getDetail();
    },
    toH5(e) {
      switch (e.type) {
        case 1:
          //招标公告
          this.$u.func.route('/pagesPackage/searchDetail/searchDetail?id=' + e.businessId + '&content=' + this.query.content + '&name=' + e.projectName)
          break;
        case 2:
          //中标候选人详情
          this.$u.func.route('/pagesPackage/candidateDetaill/candidateDetaill?id=' + e.businessId + '&content=' + this.query.content + '&name=' + e.projectName)
          break;
        case 3:
          //中标结果详情
          this.$u.func.route('/pagesPackage/resultDetail/resultDetail?id=' + e.businessId + '&content=' + this.query.content + '&name=' + e.projectName)
          break;
      }
    },
    binddateChange: function (e) {
      this.query.date = e.detail.value
      this.query.current = 1
      this.getDetail()
    },
    //查询详情
    getDetail() {
      uni.showLoading({})
      if (this.query.type == 0) {
        this.query.type = ''
      }
      if (this.query.region == '地区') {
        this.query.region = ''
      }
      this.$u.api.esPage(this.query).then(data => {
        if (data.success) {
          uni.hideLoading()
          this.pages = data.data.pages //总页数
          let arr = data.data.list
          if (arr.length == 0) {
            this.result = []
          } else {
            this.result = this.result.concat(...arr)
          }
          if (this.query.current >= this.pages) {
            this.status = 'nomore';
          }
        }
      }).catch(err => {
		uni.hideLoading()
		if (err.data.code === 401) {
		  uni.showToast({
		    title: '登录已过期，请重新登录！',
		    duration: 3000,
		    icon: 'none'
		  })
		  uni.navigateTo({
		    url: '/pages/login/login-account'
		  })
		}else{
			uni.showToast({
			  title: err.data.msg,
			  icon: 'none'
			});
			this.$u.func.showToast({
			  title: err,
			})
		}
      })
    },
    areaChange(e) {
      this.query.current = 1
      this.areaValue = e.detail.value;
      this.query.region = this.area[e.detail.value].name;
      this.result = []
      this.getDetail()
    },
    bindPickChange(e) {
      this.query.current = 1
      this.query.type = e.detail.value;
      this.statusValue = e.detail.value;
      this.result = []
      this.getDetail()
    },
  }
}
</script>

<style lang="scss">
.news-list {
  .news-item {
    &:not(:last-of-type) {
      padding: 0 0 30rpx;
      margin-bottom: 30rpx;
    }

    background: #fff;
    border-radius: 4px;
    padding: 20rpx;
    margin: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .right {
      flex: 1;
      height: 160rpx;
      display: flex;
      justify-content: space-around;
      position: relative;

      .info {
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #585b61;
      }

      .date {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #a6abb5;

        .icon {
          width: 21rpx;
          height: 21rpx;
          margin-right: 9rpx;
        }
      }
    }
  }
}

.region {
  color: #0bb9c8;
  position: absolute;
  left: 0;
  bottom: 0;
  display: flex;
  font-size: 24rpx;
}

.flex-1 {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
}

.title {
  color: red;
  text-align: center;
  font-size: 26rpx;
  letter-spacing: 2px;
  background: #fff;
  padding-bottom: 14rpx;
}

.number {
  position: absolute;
  bottom: 36rpx;
  left: 0;
  width: 180rpx;
  height: 32rpx;
  line-height: 32rpx;
  font-size: 24rpx;
  text-align: center;
  border-radius: 2px;
  font-weight: 500;
}

.sum {
  position: absolute;
  bottom: 36rpx;
  right: 0;
  width: 200rpx;
  height: 34rpx;
  line-height: 34rpx;
  font-size: 24rpx;
  text-align: right;
  border-radius: 2px;
  font-weight: 500;
  color: #000000;
}
</style>