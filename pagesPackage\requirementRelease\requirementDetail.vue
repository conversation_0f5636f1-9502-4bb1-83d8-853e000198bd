<template>
	<view class="req-detail-container">
		<view class="header-box"
			v-if="openType === 'my' && requirementData.putStatus === 1 && requirementData.finalStatus === 0">
			<view v-if="requirementData.auditStatus === 1" class="header-btn" @click="handleFinal">已完成对接</view>
			<view class="header-btn" @click="handleEdit">编辑</view>
		</view>
		<!-- 表单内容 -->
		<view class="form-container">
			<!-- <view class="form-item">
				<view class="label">姓名</view>
				<view class="value">{{ requirementData.publisherName }}</view>
			</view>

			<view class="form-item">
				<view class="label">手机号</view>
				<view class="value">{{ requirementData.phone }}</view>
			</view>

			<view class="form-item">
				<view class="label">单位名称</view>
				<view class="value">{{ requirementData.unitName }}</view>
			</view>

			<view class="form-item">
				<view class="label">发布者属性</view>
				<view class="value">{{ getDictLabel(requirementData.publisherType, publisherAttrOptions) }}</view>
			</view>

			<view class="form-item">
				<view class="label">岗位职务</view>
				<view class="value">{{ getDictLabel(requirementData.jobTitle, jobTitleOptions) }}</view>
			</view> -->

			<view class="form-item">
				<view class="label">有效时间</view>
				<view class="value">{{requirementData.expireTime && requirementData.expireTime.split(' ')[0] }}</view>
			</view>

			<view class="form-item">
				<view class="label">需求类别</view>
				<view class="value">
					<text>{{ currentCategoryName }}</text>
				</view>
			</view>

			<view class="form-item">
				<view class="label">需求主题</view>
				<view class="value">{{ requirementData.demandTitle }}</view>
			</view>

			<view class="form-item">
				<view class="label">查找对象</view>
				<view class="value">{{ requirementData.targetObject }}</view>
			</view>

			<view class="form-item">
				<view class="label">对象属性</view>
				<view class="value">{{ getDictLabel(requirementData.objectProperty, publisherAttrOptions) }}</view>
			</view>

			<view class="form-item" v-if="isShowMajor">
				<view class="label">专业</view>
				<view class="value">{{ requirementData.major }}</view>
			</view>

			<view class="form-item">
				<view class="label">项目地点</view>
				<view class="value">
					{{ projectLocation }}
				</view>
			</view>

			<view class="form-item">
				<view class="label">公司名称</view>
				<view class="value">{{ requirementData.companyName }}</view>
			</view>

			<view class="form-item">
				<view class="label">材料设备名称+品牌名称</view>
				<view class="value">{{ requirementData.brandName }}</view>
			</view>

			<view class="form-item">
				<view class="label">微信文章链接</view>
				<view class="value">{{ requirementData.wxPublicRdUrl }}</view>
			</view>

			<view class="form-item">
				<view class="label">需求数量</view>
				<view class="value">{{ requirementData.quantity }}</view>
			</view>

			<view class="form-item" v-if="isShowQualification">
				<view class="label">工程资质</view>
				<view class="value">{{ getDictLabel(requirementData.qualification, qualificationOptions) }}</view>
			</view>

			<view class="form-item" v-if="isShowQualification">
				<view class="label">工程资质描述</view>
				<view class="value">{{ requirementData.qualificationRemark }}</view>
			</view>

			<view class="form-item" v-if="isShowCertificate">
				<view class="label">证书/职称</view>
				<view class="value">{{ getDictLabel(requirementData.certificateTitle, certificateOptions) }}</view>
			</view>

			<view class="form-item" v-if="isShowCertificate">
				<view class="label">证书/职称描述</view>
				<view class="value">{{ requirementData.ctremark }}</view>
			</view>

			<view class="project-images" v-if="demandImgArray.length > 0">
				<view class="label">项目图片</view>
				<view class="image-container">
					<view class="image-item" v-for="(img, index) in demandImgArray" :key="index">
						<image :src="img" mode="aspectFill" @click="previewImage(img)"></image>
					</view>
				</view>
			</view>

			<view class="">
				<view class="label">需求描述</view>
				<view class="textarea-value">
					{{ requirementData.description || '' }}
				</view>
			</view>

			<view class="">
				<view class="label">其他要求</view>
				<view class="textarea-value">
					{{ requirementData.demandRemark || '' }}
				</view>
			</view>
		</view>

		<!-- 留言区域 -->
		<view class="comments-section">
			<view class="comments-header">
				<view class="comments-title">
					<text>留言</text>
					<text v-if="requirementData.createUser === userInfo.user_id">({{ totalComments }})</text>
				</view>
				<view class="comment-icon">
					<image src="../../static/icon/filter.png" mode="aspectFit" @click="filterVisible = true"></image>
					<image v-if="openType === 'look'" src="../../static/icon/message.png" mode="aspectFit"
						@click="showCommentPopup"></image>
				</view>
			</view>

			<scroll-view class="comments-scroll" scroll-y @scrolltolower="loadMoreComments"
				:style="{ height: commentsScrollHeight + 'px' }">
				<view class="comments-list">
					<!-- 留言项 -->
					<view class="comment-item" v-for="(comment, index) in comments" :key="index">
						<view class="comment-avatar">
							<image :src="comment.avatar || '../../static/images/tabbar/user_selected.png'"
								mode="aspectFill">
							</image>
						</view>
						<view class="comment-content">
							<view class="comment-header">
								<text class="comment-name">{{ comment.leaveMessageName }}</text>
								<text
									class="comment-org">{{ getDictLabel(comment.messageUserType, publisherAttrOptions) }}</text>
							</view>
							<view class="comment-text">
								{{ comment.content }}
							</view>
							<view class="comment-time">
								{{ comment.leaveMessageTime }}
							</view>
						</view>
					</view>
					<!-- 加载状态 -->
					<view class="loading-more">
						<u-loadmore :status="loadMoreStatus" />
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 留言弹窗 -->
		<u-popup :show="showPopup" mode="bottom" :round="16">
			<view class="comment-popup">
				<view class="popup-header">
					<text>请填写留言描述</text>
					<view class="close-btn" @click="showPopup = false">
						<uni-icons type="closeempty" size="24" color="#444444"></uni-icons>
					</view>
				</view>
				<view class="popup-content">
					<uni-forms ref="commentForm" label-width="120" :model="commentForm" :rules="commentRules">
						<uni-forms-item label="留言内容" required name="content">
							<uni-easyinput v-model="commentForm.content" type="textarea" placeholder="请填写留言描述" />
						</uni-forms-item>

						<uni-forms-item label="联系方式" required name="contactInfo">
							<view class="value">{{userInfo.userPhone }}</view>
						</uni-forms-item>

						<uni-forms-item label="留言者属性" required name="messageUserType">
							<view class="value">{{ getDictLabel(userInfo.publisherType, publisherAttrOptions) }}</view>
						</uni-forms-item>

						<uni-forms-item label="留言图片" required name="recommendImage">
							<Urlimg v-if="showPopup" :size="120" :height="120" :imgList="commentForm.recommendImage" :max="3" @getImage="uploadFile">
							</Urlimg>
						</uni-forms-item>
					</uni-forms>

					<view class="submit-box">
						<view class="submit-btn" @click="sendComment">提交</view>
					</view>
				</view>
			</view>
		</u-popup>
		<filter-popup :visible.sync="filterVisible" :filterData="filterData" @confirm="handleFilterConfirm"
			@close="filterVisible = false">
		</filter-popup>
	</view>
</template>

<script>
	import FilterPopup from '@/pages/home/<USER>';
	import Urlimg from "../components/urlImg.vue"

	export default {
		components: {
			FilterPopup,
			Urlimg
		},
		data() {
			return {
				demandId: '',
				requirementData: {},
				comments: [],
				commentForm: {
					leaveMessageName: '', // 留言人名称
					content: '', // 留言内容
					contactInfo: '', // 联系方式
					messageUserType: '', // 留言者属性
					isRecommend: false, // 是否推荐需求
					recommendCompany: '', // 推荐公司
					recommendPhone: '' // 推荐公司联系方式
				},
				commentRules: {
					content: {
						rules: [{
							required: true,
							errorMessage: '请填写留言内容'
						}, {
							minLength: 5,
							maxLength: 200,
							errorMessage: '留言内容长度在5-200个字符之间'
						}]
					}
				},
				commentParams: {
					current: 1,
					size: 10,
					demandId: ''
				},
				totalComments: 0,
				totalPages: 1,
				loadMoreStatus: 'loadmore',
				commentsScrollHeight: 400,
				showPopup: false,
				MessageTypeList: [],
				filterVisible: false,
				filterData: [],
				publisherAttrOptions: [],
				majorOptions: [],
				jobTitleOptions: [],
				qualificationOptions: [],
				certificateOptions: [],
				categoryOptions: [],
				openType: '',
			}
		},
		computed: {
			projectLocation() {
				return (this.requirementData.provinceName || '') + (this.requirementData.cityName || '') + (this.requirementData.districtName || '') + (this.requirementData.projectLocation || '')
			},
			demandImgArray() {
				if (!this.requirementData.demandImg) return [];
				return this.requirementData.demandImg.split(',');
			},
			isShowQualification() {
				return this.currentCategoryName === '工程资质买卖和办理';
			},
			isShowCertificate() {
				return this.currentCategoryName === '证书/职称';
			},
			isShowMajor() {
				return this.currentCategoryName === '材料设备供应商';
			},
			currentCategoryName() {
				if (!this.requirementData.categoryId || !this.categoryOptions.length) return '';
				const category = this.categoryOptions.find(item => item.value === this.requirementData.categoryId);
				return category ? category.text : '';
			}
		},
		onLoad(options) {
			this.demandId = options.id;
			this.commentParams.demandId = options.id;
			this.openType = options.openType;
			this.calcScrollHeight();
		},
		onShow() {
			const {
				scene
			} = uni.getLaunchOptionsSync();
			if (scene === 1154) {
				uni.showToast({
					title: '请点击右下角"前往小程序>"查看',
					icon: 'error',
					duration: 5000
				})
				return;
			}
			if (this.demandId) {
				this.getRequirementDetail();
				this.getComments();
				this.getMessageType();
				this.getDictionary();
				this.getCategoryOptions();
			}
		},
		onReady() {
			this.calcScrollHeight();
		},
		onShareAppMessage() {
			return {
				title: this.requirementData.demandTitle,
				path: '/pagesPackage/requirementRelease/requirementDetail?id=' + this.demandId,
			}
		},
		onShareTimeline() {
			return {
				title: this.requirementData.demandTitle,
				path: '/pagesPackage/requirementRelease/requirementDetail?id=' + this.demandId,
			}
		},
		methods: {
			uploadFile(e) {
				console.log(e);
				// this.imgList = e
				const iMgList = e
				this.commentForm.recommendImage = iMgList
				console.log('this.imgList ', this.commentForm.recommendImage)
				if (JSON.stringify(e) === "[]") {
					this.commentForm.recommendImage = ''
				} else {
					this.commentForm.recommendImage = e.join(',')
				}
			},
			getCategoryOptions() {
				this.$u.api.getCategoryList(1, -1).then(data => {
					this.categoryOptions = data.data.records.map(item => ({
						text: item.categoryName,
						value: item.id
					}))
				})
			},
			handleFinal() {
				uni.showModal({
					title: '确认提示',
					content: '确认已完成对接吗？',
					confirmText: '确认',
					cancelText: '取消',
					success: res => {
						if (res.confirm) {
							this.$u.api.isFinalDemand({
								id: this.demandId,
								finalStatus: 1
							}).then(res => {
								if (res.code === 200) {
									uni.showToast({
										title: '已完成对接',
										icon: 'success'
									})
									this.getRequirementDetail();
								} else {
									uni.showToast({
										title: res.msg,
										icon: 'none'
									})
								}
							}).catch(err => {
								uni.showToast({
									title: err.msg,
									icon: 'none'
								})
							})
						}
					}
				})
			},
			handleEdit() {
				this.$u.func.route(
					`/pagesPackage/requirementRelease/addRequirement?formData=${JSON.stringify(this.requirementData)}`)
			},
			// 添加回显函数，用于字典数据回显
			getDictLabel(value, options) {
				if (!value || !options || !options.length) {
					return value;
				}
				const found = options.find(item => item.value === value);
				return found ? found.text : value;
			},
			async getDictionary() {
				// 字典类型与对应选项的映射
				const dictMapping = {
					'publisher_type': 'publisherAttrOptions',
					'major': 'majorOptions',
					'job_title': 'jobTitleOptions',
					'engineering_qualification': 'qualificationOptions',
					'certificate': 'certificateOptions',
				};

				try {
					// 创建并行请求的Promise数组
					const promises = Object.entries(dictMapping).map(async ([code, optionName]) => {
						try {
							const res = await this.$u.api.getDictionaryBiz({
								code
							});
							this[optionName] = res.data.map(item => ({
								text: item.dictValue,
								value: item.dictKey
							}));
						} catch (err) {
							console.error(`获取${code}字典数据失败:`, err);
						}
					});

					// 等待所有请求完成
					await Promise.all(promises);
				} catch (error) {
					console.error('获取字典数据失败:', error);
				}
			},
			saveDemandHistory() {
				this.$u.api.saveDemandHistory({
					userId: this.userInfo.user_id,
					demandId: this.demandId
				});
			},
			getMessageType() {
				// 获取留言者属性字典数据
				this.$u.api.getDictionaryBiz({
					code: 'publisher_type'
				}).then(res => {
					// 设置留言表单的选择器数据
					this.MessageTypeList = res.data.map(item => ({
						text: item.dictValue,
						value: item.dictKey
					}));

					// 构建筛选数据
					this.filterData = [{
						title: '留言者属性',
						code: 'publisher_type',
						multiSelect: false,
						children: res.data.map(dict => ({
							id: dict.dictKey,
							name: dict.dictValue
						}))
					}];

					// 添加留言时间筛选条件
					this.filterData.push({
						title: '留言时间',
						code: 'time',
						children: []
					});
				}).catch(err => {
					console.error('获取留言者属性字典数据失败:', err);
					uni.showToast({
						title: '获取筛选数据失败',
						icon: 'none'
					});
				});
			},
			calcScrollHeight() {
				const systemInfo = uni.getSystemInfoSync();
				this.commentsScrollHeight = systemInfo.windowHeight * 0.4;
			},
			goBack() {
				uni.navigateBack();
			},
			getRequirementDetail() {
				if (!this.demandId) return;

				this.$u.api.getDemandDetail({
					id: this.demandId
				}).then(res => {
					this.requirementData = res.data;
					if (res.data.createUser !== this.userInfo.user_id) {
						this.saveDemandHistory();
					}
				}).catch(err => {
					uni.showToast({
						title: '获取需求详情失败',
						icon: 'none'
					});
				});
			},
			getComments() {
				if (!this.demandId) return;

				this.loadMoreStatus = 'loading';
				uni.showLoading({
					title: '加载中'
				});

				this.$u.api.getMessageList({
					...this.commentParams
				}).then(res => {
					if (this.commentParams.current === 1) {
						this.comments = res.data.records || [];
					} else {
						this.comments = [...this.comments, ...(res.data.records || [])];
					}

					this.totalComments = res.data.total || 0;
					this.totalPages = res.data.pages || 1;

					if (this.commentParams.current >= this.totalPages) {
						this.loadMoreStatus = 'nomore';
					} else {
						this.loadMoreStatus = 'loadmore';
					}

					uni.hideLoading();
				}).catch(err => {
					console.error('获取评论失败', err);
					this.loadMoreStatus = 'loadmore';
					uni.hideLoading();
					uni.showToast({
						title: '获取评论失败',
						icon: 'none'
					});
				});
			},
			loadMoreComments() {
				if (this.loadMoreStatus === 'nomore' || this.loadMoreStatus === 'loading') {
					return;
				}

				if (this.commentParams.current < this.totalPages) {
					this.commentParams.current++;
					this.getComments();
				}
			},
			showCommentPopup() {
				// 清空表单
				this.commentForm = {
					leaveMessageName: '',
					content: '',
					contactInfo: '',
					messageUserType: '',
					recommendImage: ''
				};
				this.showPopup = true;
				this.$nextTick(() => {
					this.$refs.commentForm.setRules(this.commentRules)
				})
			},
			sendComment() {
				console.log(this.commentForm.recommendPhone);

				this.$refs.commentForm.validate().then(res => {
					uni.showLoading({
						title: '发送中'
					});

					this.$u.api.addComment({
						leaveMessageName: this.userInfo.userName,
						userId: this.userInfo.user_id,
						demandId: this.demandId,
						content: this.commentForm.content,
						contactInfo: this.userInfo.userPhone,
						messageUserType: this.userInfo.publisherType,
						recommendImage: this.commentForm.recommendImage
					}).then(res => {
						uni.hideLoading();
						uni.showToast({
							title: '留言成功',
							icon: 'success'
						});
						this.commentForm = {
							leaveMessageName: '',
							content: '',
							contactInfo: '',
							messageUserType: '',
							recommendImage: ''
						};
						this.showPopup = false;
						this.commentParams.current = 1;
						this.getComments();
					}).catch(err => {
						uni.hideLoading();
						uni.showToast({
							title: '留言失败',
							icon: 'none'
						});
					});
				}).catch(err => {
					console.log('表单验证失败', err);
				});
			},
			previewImage(current) {
				uni.previewImage({
					urls: this.demandImgArray,
					current: current
				});
			},
			handleFilterConfirm(selectedItems) {
				// 处理筛选数据
				console.log('筛选条件:', selectedItems);
				this.commentParams = {
					current: this.commentParams.current,
					size: this.commentParams.size,
					demandId: this.commentParams.demandId,
					...selectedItems
				}

				this.commentParams.messageUserType = selectedItems.publisher_type || '';
				this.filterVisible = false;
				// 根据筛选条件重新查询数据
				this.commentParams.current = 1;
				this.comments = [];
				this.getComments();
			}
		}
	}
</script>

<style lang="scss" scoped>
	view {
		box-sizing: border-box;
	}

	.req-detail-container {
		padding-bottom: 20rpx;
		background-color: #fff;
	}

	.header-box {
		width: 100%;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		padding: 24rpx 32rpx;
		box-sizing: border-box;

		.header-btn {
			width: 162rpx;
			height: 64rpx;
			border-radius: 8rpx;
			background: #4075FF;
			font-family: Source Han Sans;
			font-size: 26rpx;
			font-weight: normal;
			line-height: normal;
			letter-spacing: normal;
			color: #FFFFFF;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-left: 40rpx;
		}
	}

	.back-btn {
		width: 40rpx;
		height: 40rpx;
	}

	.back-btn image {
		width: 100%;
		height: 100%;
	}

	.header-title {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		font-size: 32rpx;
		font-weight: bold;
	}

	.form-container {
		background-color: #fff;
		padding: 0 32rpx;
		margin-bottom: 88rpx;
	}

	.form-item {
		height: 112rpx;
		display: flex;
		box-sizing: border-box;
	}

	.label {
		width: 230rpx;
		font-size: 28rpx;
		color: #333;
		padding: 32rpx 0;
	}

	.value {
		flex: 1;
		font-size: 28rpx;
		color: #666;
		border-bottom: 2rpx solid #F5F5F5;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		padding: 32rpx 0;
	}

	.image-container {
		display: flex;
		flex-wrap: wrap;
		margin-top: 10rpx;
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		/* gap: 25rpx; */
	}

	.image-item {
		width: 206rpx;
		height: 206rpx;
		margin-bottom: 25rpx;
	}

	.image-item image {
		width: 100%;
		height: 100%;
		border-radius: 8rpx;
	}

	.textarea-value {
		height: 218rpx;
		padding: 16rpx 48rpx;
		border-radius: 8rpx;
		background: #F4F6F8;
		overflow-y: auto;
		font-family: Source Han Sans;
		font-size: 26rpx;
		font-weight: normal;
		line-height: 181.15%;
		letter-spacing: normal;
		color: #333333;
	}

	.textarea-container {
		width: 100%;
		min-height: 150rpx;
		border: 1rpx solid #eee;
		border-radius: 10rpx;
		padding: 10rpx;
		margin-top: 10rpx;
	}

	textarea {
		width: 100%;
		height: 150rpx;
	}

	.comments-section {
		/* margin-top: 20rpx; */
		background-color: #fff;
		padding: 20rpx 32rpx;
	}

	.comments-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 20rpx;
		position: relative;
	}

	.comments-title {
		display: flex;
		align-items: center;
		font-family: Source Han Sans;
		font-size: 32rpx;
		font-weight: 500;
		line-height: normal;
		letter-spacing: normal;
		color: #333333;
	}

	.comment-icon {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.comment-icon image {
		width: 32rpx;
		height: 32rpx;
		margin-left: 40rpx;
	}

	.comments-scroll {
		width: 100%;
	}

	.comments-list {
		width: 100%;
	}

	.loading-more {
		text-align: center;
		margin: 20rpx 0;
	}

	.comment-item {
		display: flex;
		margin-bottom: 44rpx;
	}

	.comment-avatar {
		width: 64rpx;
		height: 64rpx;
		border-radius: 50%;
		overflow: hidden;
		flex-shrink: 0;
	}

	.comment-avatar image {
		width: 100%;
		height: 100%;
		border-radius: 50%;
	}

	.comment-content {
		flex: 1;
		margin-left: 18rpx;
	}

	.comment-header {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.comment-name {
		font-family: Source Han Sans;
		font-size: 26rpx;
		font-weight: normal;
		line-height: normal;
		letter-spacing: normal;
		color: #999999;
	}

	.comment-org {
		padding: 6rpx 18rpx;
		margin-left: 18rpx;
		height: 40rpx;
		border-radius: 4rpx;
		background: #325AEE;
		display: flex;
		align-items: center;
		justify-content: center;
		font-family: Source Han Sans;
		font-size: 20rpx;
		font-weight: normal;
		line-height: normal;
		letter-spacing: normal;
		color: #FFFFFF;
	}

	.comment-text {
		font-family: Source Han Sans;
		font-size: 26rpx;
		font-weight: 500;
		line-height: 160.75%;
		letter-spacing: 0.12em;
		color: #333333;
		padding: 0 16rpx;
	}

	.comment-time {
		font-family: Source Han Sans;
		font-size: 20rpx;
		margin-top: 20rpx;
		font-weight: 500;
		line-height: 160.75%;
		letter-spacing: 0.12em;
	}

	/* 留言弹窗样式 */
	.comment-popup {
		background-color: #fff;
		padding: 30rpx 30rpx 30rpx 30rpx;
		border-radius: 16rpx 16rpx 0 0;
		box-sizing: border-box;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.popup-header text {
		font-size: 32rpx;
		font-weight: bold;
	}

	.close-btn {
		width: 40rpx;
		height: 40rpx;
	}

	.close-btn image {
		width: 100%;
		height: 100%;
	}

	.popup-content {
		padding: 0 10rpx;
	}

	.submit-box {
		width: 100%;
		display: flex;
		justify-content: center;
	}

	.submit-btn {
		width: 688rpx;
		height: 66rpx;
		border-radius: 624rpx;
		background: #325AEE;
		display: flex;
		justify-content: center;
		align-items: center;
		font-family: Source Han Sans;
		font-size: 26rpx;
		font-weight: 500;
		line-height: normal;
		text-align: center;
		letter-spacing: normal;
		color: #FFFFFF;
	}
</style>