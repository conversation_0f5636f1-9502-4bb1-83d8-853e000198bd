<template>
	<div class="container">
		<view v-for="(tableData, tableIndex) in tables" :key="tableIndex" class="table-wrapper">
			<scroll-view class="table-container" scroll-x="true" show-scrollbar="true">
				<view class="table">
					<view class="table-row table-header">
						<view class="table-cell" v-for="(cell, index) in tableData.header" :key="index">{{ cell.text }}
						</view>
					</view>
					<view class="table-row" v-for="(row, rowIndex) in tableData.body" :key="rowIndex">
						<view class="table-cell" v-for="(cell, cellIndex) in row" :key="cellIndex"
							v-html="formatText(cell.text)"></view>
					</view>
				</view>
			</scroll-view>
		</view>
	</div>
</template>

<script>
export default {
	props: {
		content: {
			type: String,
			default: ''
		},
		detailId: {
			type: String,
			default: '1928382081211977730'
		}
	},
	data() {
		return {
			jsonData: [{
				"data": [
					["序号", "材料(设备)名称", "招标人推荐品牌或生产厂", "备注及技术要求", "投标人选定生产厂或品牌【必须填报,且只能填一个】"],
					["一、土建", "", "", "", ""],
					["1", "混凝土微膨胀剂", "杭州卓然、杭州奔泰、浙江合力、武汉三源、或相当于", "", ""],
					["2", "混凝土抗裂纤维", "上海岩磐、上海美梦佳、武汉三源、或相当于", "", ""],
					["3", "页岩砖", "临安市大唐新型墙体材料有限公司、杭州迪亿建材有限公司、湖州秦汉新型建筑材料有限公司、杭州黄湖砖瓦厂、或相当于", "", ""],
					["4", "保温砂浆", "杭州固盾、杭州天翔、臣功、雁翔、逐峰、或相当于", "", ""],
					["5", "ALC墙板", "杭加、开元、伊通、或相当于", "", ""],
					["6", "防锈漆、金属漆", "阿克苏诺贝尔、金隅、兰陵或相当于", "", ""],
					["7", "砂加气混凝土砌块", "杭加、开元、伊通、或相当于", "", ""],
					["8", "环氧树脂耐磨地坪漆", "西卡(SIKA)、施贝化学(SpecPoxy)、巴斯夫(BASF)、或相当于", "", ""],
					["9", "防水卷材、防水涂料", "东方雨虹、科顺、卓宝、或相当于", "", ""],
					["10", "防火岩棉、保温岩棉", "欧文斯科宁(OWENSCORNING)、依索维尔(ISOVER)、洛科威(Rockwool)、或相当于", "", ""],
					["11", "挤塑聚苯板", "杭州泰富龙、杭州希尔特、南京法宁格、", "", ""]
				]
			}, {
				"data": [
					["", "", "北鹏首豪、或相当于", "", ""],
					["12", "防霉涂料", "多乐士、立邦、大师、华润、或相当于", "", ""],
					["13", "真石漆、多彩仿石涂料、外墙质感涂料", "三棵树、亚士漆、立邦、或相当于", "", ""],
					["14", "防火卷帘门(含电机)", "杭州新欣门业、杭州锦绣前程、杭州振兴、上海蓝盾、或相当于;卷帘门电机:江西三星阿兰德、漳州天宇、漳州杰龙、或相当于", "", ""],
					["15", "无机涂料", "多乐士、立邦、大师、华润、或相当于", "", ""],
					["16", "乳胶漆", "多乐士、立邦、大师、华润、或相当于", "", ""],
					["17", "玻璃棉", "上海樱花、广州西斯尔、杭州泰富龙、或相当于", "", ""],
					["18", "成品实木门", "TATA木门、大自然、兔宝宝或相当于", "", ""],
					["19", "玻璃", "南玻、耀皮、信义、台玻长江、或相当于", "", ""],
					["20", "铝合金型材", "亚洲铝材(亚铝)、广东兴发、福建南平铝业、广东凤铝、或相当于", "", ""],
					["21", "门窗五金", "格屋、ROTO诺托、HOPPE好博、或相当于", "", ""],
					["22", "人防门及人防防护设备", "浙江叁益、杭州钱江人防、杭州人防、绍兴金盾、或相当于", "", ""],
					["23", "预制实心方桩", "浙江正大、新业管桩、海通管桩、或相当于", "", ""],
					["24", "防腐涂料", "多乐士、立邦、大师、华润、或相当于", "", ""],
					["25", "膨胀型防火涂料", "阿克苏诺贝尔、金隅、兰陵、或相当于", "", ""]
				]
			}, {
				"data": [
					["26", "预制叠合板", "中民筑友、杭州远大、中天、宝业、或相当于", "", ""],
					["二、幕墙、门窗", "", "", "", ""],
					["27", "保温岩棉、防火岩棉", "同\"一、土建\"", "", ""],
					["28", "吸音棉(玻璃棉)", "同\"一、土建\"", "", ""],
					["29", "铝合金型材", "同\"一、土建\"", "", ""],
					["30", "玻璃", "同\"一、土建\"", "", ""],
					["31", "幕墙不锈钢爪件、驳接件", "广东坚朗、沈阳恒安、宁波东天、或相当于", "", ""],
					["32", "幕墙铝单板", "上海西蒙、浙江墙煌、西安西飞、广东高士达、或相当于", "", ""],
					["33", "PVB胶片", "杜邦、首诺、佳士福、或相当于", "", ""],
					["34", "密封胶条、密封垫块", "江阴海达、宁波新安东、青岛美德、或相当于", "", ""],
					["35", "密封胶、结构胶、耐候胶", "道康宁(陶熙DOWSIlL)、西卡(Sika)、GE、或相当于", "", ""],
					["36", "防火密封胶", "喜利得(HILTI)、麦塔克(Metacaulk)、道康宁(DowCorning)、或相当于", "", ""],
					["37", "玻璃窗五金", "格屋、ROTO诺托、HOPPE好博、或相当于", "", ""],
					["38", "玻璃门五金件(含地弹簧、地锁及执手等)", "多玛(DORMA)、盖泽(GEZE)、皇冠(CROWN)、或相当于", "", ""],
					["39", "化学锚栓、机械锚栓、石材背栓", "喜利得(HILTI)、慧鱼(FISCHER)、伍尔特(WURTH)、或相当于", "", ""],
					["40", "不锈钢紧固件(螺钉、螺栓等)", "索斯科(Southco)、玛托(Morcato)、慧鱼", "", ""]
				]
			}, {
				"data": [
					["", "", "(FISCHER)、或相当于", "", ""],
					["41", "氟碳涂料、粉末涂料", "PPG、阿克苏-诺贝尔(AkzoNobel)、SKK、老虎(TIGER)、或相当于", "", ""],
					["42", "电动开窗机", "D+H、盖泽(GEZE)、格屋(G-U)、或相当于", "", ""],
					["43", "自动感应门感应装置", "多玛(DORMA)、盖泽(GEZE)、纳博克(NABCO)、或相当于", "", ""],
					["44", "单晶硅光伏板", "晶科能源、隆基股份、晶澳科技、通威股份、或相当于", "", ""],
					["45", "穿孔铝板", "上海西蒙、浙江墙煌、西安西飞、广东高士达、或相当于", "", ""],
					["", "孔铝板", "", "", ""],
					["三、泛光照明", "", "", "", ""],
					["46", "LED灯具", "欧司朗(OSRAM)、依古姿妮、百家丽(Beghelli)、或相当于", "", ""],
					["47", "LED灯具芯片(光源)", "欧司朗(OSRAM)、飞利浦(PHILIPS)、科锐(CREE)、或相当于", "", ""],
					["48", "LED灯具驱动电源", "台湾明纬、茂硕、英飞特、或相当于", "", ""],
					["49", "配电控制系统(总控电脑、交换机、网关、模块电源、智能强电开关模块)", "瑞琦、永林、新光、或相当于", "", ""],
					["50", "电缆、电线", "浙江万马、中大元通、江苏远东、江苏上上、或相当于", "", ""],
					["51", "桥架", "浙江远大、浙江桥母、浙江浩顺、或相当于", "", ""]
				]
			}, {
				"data": [
					["穿孔铝板"],
					["孔铝板"]
				]
			}, {
				"data": [
					["52", "配管(镀锌钢管)", "浙江金洲、天津利达、上海劳动、或相当于", "", ""],
					["53", "配电箱(柜)", "浙江金盾电器、杭州杭开电气、浙宝电气、杭州电力设备制造有限公司、浙江白象电器或相当于", "", ""],
					["54", "配电箱(柜)元器件", "施耐德、ABB、西门子、或相当于", "", ""],
					["55", "光纤、超五类屏蔽网线", "康普、泛达、西蒙、或相当于", "", ""],
					["56", "电气保护箱", "浙江金盾电器、杭州杭开电气、浙宝电气、杭州电力设备制造有限公司、浙江白象电器、或相当于", "", ""],
					["四、安装支架", "", "", "", ""],
					["57", "抗震支架", "浙江旗鱼、浙江海迈、深圳优力可、或相当于", "", ""],
					["58", "综合支吊架", "浙江旗鱼、浙江海迈、深圳优力可、或相当于", "", ""],
					["五、电气", "", "", "", ""],
					["59", "照明灯具(含光源)", "同\"十、精装修\"", "", ""],
					["60", "开关、插座", "同\"十、精装修\"", "", ""],
					["61", "配电柜(箱)成套厂", "浙江金盾电器、杭州杭开电气、浙宝电气、杭州电力设备制造有限公司、浙江白象电器、或相当于", "", ""],
					["62", "SC(热镀锌钢管)、JDG 管", "杭州天一、武陵源、萧通、或相当于", "", ""],
					["63", "塑壳断路器", "施耐德、ABB、西门子、或相当于", "", ""],
					["64", "计量表、数显表", "施耐德、ABB、西门子、或相当于", "", ""],
					["65", "微型断路器", "施耐德、ABB、西门子、或相当于", "", ""]
				]
			}],
			tables: [],
		};
	},
	onLoad() {
		
	},
	mounted() {
		this.$u.api.getEsDetailHtml({ id: this.detailId, content: this.content }).then(res => {
			this.jsonData = res.data[0];
			console.log(this.jsonData)
			this.generateTables();
		})
	},
	methods: {
		generateTables() {
			this.tables = this.jsonData.map(item => {
				const data = item.data;

				// 确保数据至少有一行
				if (!data || data.length === 0) {
					return {
						header: [],
						body: []
					};
				}

				// 将原始数据转换为对象格式
				const processedData = data.map(row => {
					return row.map(cellText => {
						return { text: cellText || '' };
					});
				});

				return {
					header: processedData[0],
					body: processedData.slice(1)
				};
			});
		},
		formatText(text) {
			if (!text) return '';

			// 首先处理换行符
			let formattedText = text.replace(/\r/g, '<br>');

			// 如果有高亮关键词，则进行高亮处理
			if (this.content && this.content.trim()) {
				const keyword = this.content.trim();
				// 使用正则表达式进行全局替换，忽略大小写
				const regex = new RegExp(`(${keyword})`, 'gi');
				formattedText = formattedText.replace(regex, '<span style="color: red;">$1</span>');
			}

			return formattedText;
		}
	}
};
</script>

<style lang="scss" scoped>
.container {
	width: 100vw;
height: 100vh;
	padding-top: 220rpx;
}

.table-wrapper {
	margin-bottom: 40rpx;
}

.table-wrapper:first-child .table-header .table-cell {
	background-color: #c4bfbf;
	font-weight: bold;
}

.table-container {
	width: 100%;
	white-space: nowrap;
	overflow-x: auto;
}

.table {
	width: 100%;
	min-width: 750rpx;
	border-collapse: collapse;
	border: 1px solid #eee;
	table-layout: fixed;
}

.table-row {
	display: flex;
	border-bottom: 1px solid #eee;
}

.table-cell {
	padding: 20rpx;
	border-right: 1px solid #eee;
	font-size: 28rpx;
	min-width: 200rpx;
	white-space: normal;
	word-wrap: break-word;
	word-break: break-word;
	overflow: hidden;
	display: flex;
	align-items: center;
	text-overflow: ellipsis;
	box-sizing: border-box;
	max-height: 200rpx;
}

.table-cell:nth-child(1) {
	flex: 1;
	min-width: 200rpx;
}

.table-cell:nth-child(2) {
	flex: 2;
	min-width: 250rpx;
}

.table-cell:nth-child(3) {
	flex: 3;
	min-width: 350rpx;
}

.table-cell:nth-child(4) {
	flex: 1.5;
	min-width: 200rpx;
}

.table-cell:nth-child(5) {
	flex: 2;
	min-width: 250rpx;
}

.table-cell:last-child {
	border-right: none;
}
</style>